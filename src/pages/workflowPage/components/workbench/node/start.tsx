import { NumberVariableIcon, TextVariableIcon } from "@/components/main/icon";
import { StartNodeData } from "@/types/workflow";

interface StartNodeProps {
  data: StartNodeData;
}

export default function StartNode({ data }: StartNodeProps) {
  if (!data.variables?.length) return null;
  return (
    <div className="text-xs">
      <div className="mb-1 leading-5 text-text-2">输入变量</div>
      {data.variables.map((variable) => (
        <div
          key={variable.id}
          className="mb-1 flex h-5 items-center justify-center gap-1 rounded-md border border-border-1 bg-bg-light-3 px-2 last:mb-0"
        >
          <span className="text-xs text-text-2">
            {variable.type === "number" ? (
              <NumberVariableIcon />
            ) : (
              <TextVariableIcon />
            )}
          </span>
          <span className="flex-1">{variable.name}</span>
          <span className="text-text-4">
            {variable.isRequired ? "必填" : "非必填"}
          </span>
        </div>
      ))}
    </div>
  );
}
