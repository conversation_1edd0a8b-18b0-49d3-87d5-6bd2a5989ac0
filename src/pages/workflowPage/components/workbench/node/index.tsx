import { NodeProps } from "@xyflow/react";
import { useMemo } from "react";

import { NodeType } from "@/constants/enums";
import { NodeData } from "@/types/workflow";
import { cn } from "@/utils/utils";
import <PERSON>deHandle from "./components/nodeHandle";
import Node<PERSON>eader from "./components/nodeHeader";
import EndNode from "./end";
import LLMNode from "./llm";
import StartNode from "./start";

const nodeComponentMap = {
  [NodeType.START]: StartNode,
  [NodeType.LLM]: LLMNode,
  [NodeType.END]: EndNode,
};

interface GenericNodeProps extends NodeProps {
  data: NodeData;
}

export default function GenericNode({ data, selected, id }: GenericNodeProps) {
  const NodeComponent = useMemo(() => {
    if (data.nodeType && nodeComponentMap[data.nodeType]) {
      return nodeComponentMap[data.nodeType];
    }
    return null;
  }, [data]);

  return (
    <div
      className={cn(
        "group/node flex min-h-[48px] w-[240px] flex-col gap-2 rounded-lg border border-border-1 bg-white p-3 hover:shadow-[0_6px_20px_1px_rgba(117,145,212,0.12)]",
        selected && "border-primary-default",
      )}
    >
      <NodeHeader label={data?.label} nodeType={data?.nodeType} nodeId={id} />
      <NodeComponent data={data} />
      <NodeHandle
        type="source"
        className="group-hover/node:!border-primary-default"
      />
      <NodeHandle
        type="target"
        className="group-hover/node:!border-primary-default"
      />
    </div>
  );
}
