import { EndNodeData } from "@/types/workflow";

interface EndNodeProps {
  data: EndNodeData;
}

export default function EndNode({ data }: EndNodeProps) {
  if (!data.answer) return null;
  return (
    <div className="text-xs">
      <div className="mb-1 leading-5 text-text-2">回复</div>
      <div className="rounded-md border border-border-1 bg-bg-light-3 p-2">
        {data.answer}
      </div>
    </div>
  );
}
