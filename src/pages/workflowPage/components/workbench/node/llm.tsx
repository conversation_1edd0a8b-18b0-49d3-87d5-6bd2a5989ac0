import { LLMNodeData } from "@/types/workflow";

interface LLMNodeProps {
  data: LLMNodeData;
}

export default function LLMNode({ data }: LLMNodeProps) {
  if (!data.model?.model_name) return null;
  return (
    <div className="text-xs">
      <div className="mb-1 leading-5 text-text-2">输入变量</div>
      <div className="mb-1 flex h-5 items-center justify-center gap-1 rounded-md border border-border-1 bg-bg-light-3 px-2 last:mb-0">
        <span className="text-xs text-text-2"></span>
        <span className="flex-1">{data.model?.model_name}</span>
        <span className="text-text-4">chat</span>
      </div>
    </div>
  );
}
