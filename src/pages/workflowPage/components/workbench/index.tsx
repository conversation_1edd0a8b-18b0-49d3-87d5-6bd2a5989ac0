import {
  addEdge,
  applyEdgeChanges,
  applyNodeChang<PERSON>,
  Background,
  Connection,
  Edge,
  EdgeChange,
  Node,
  NodeChange,
  ReactFlow,
} from "@xyflow/react";
import React, { useCallback, useEffect, useRef, useState } from "react";

import { NodeType } from "@/constants/enums";
import { useNodeDrag } from "@/hooks/workflow/useNodeDrag";
import { useNodePosition } from "@/hooks/workflow/useNodePosition";
import { useWorkflowStore } from "@/stores/workflowStore";
import { DragTransferData } from "@/types/workflow/node";
import CanvasOverlay from "./canvasOverlay";
import GenericNode from "./node";

const nodeTypes = {
  genericNode: GenericNode,
};

// TODO：大部分方法整合到store或hooks中
export default function Workbench() {
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const reactFlowWrapper = useRef<HTMLDivElement>(null);

  const setSelectedNodeId = useWorkflowStore(
    (state) => state.setSelectedNodeId,
  );
  const currentWorkflow = useWorkflowStore((state) => state.currentWorkflow);
  const addNode = useWorkflowStore((state) => state.addNode);

  const { screenToFlowPosition } = useNodePosition();
  const {
    handleNodeDragUpdate,
    handleNodeDragStart,
    handleNodeDragStop,
    cleanup,
  } = useNodeDrag();

  // 清理拖拽相关资源
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  useEffect(() => {
    if (currentWorkflow) {
      setNodes(
        currentWorkflow.nodes.map((node) => ({
          id: node.id,
          position: node.position,
          data: node.data,
          type: "genericNode",
        })),
      );
      setEdges(currentWorkflow.edges);
    }
  }, [currentWorkflow]);

  const onNodesChange = useCallback(
    (changes: NodeChange[]) => {
      // 应用节点变化到本地状态
      setNodes((nodesSnapshot) => applyNodeChanges(changes, nodesSnapshot));

      // 处理节点位置变化，使用优化的拖拽更新逻辑
      changes.forEach((change) => {
        if (change.type === "position" && change.position && change.dragging) {
          // 拖拽过程中使用批量防抖更新
          handleNodeDragUpdate(change.id, change.position);
        }
      });
    },
    [handleNodeDragUpdate],
  );
  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) =>
      setEdges((edgesSnapshot) => applyEdgeChanges(changes, edgesSnapshot)),
    [],
  );
  const onConnect = useCallback(
    (params: Connection) =>
      setEdges((edgesSnapshot) => addEdge(params, edgesSnapshot)),
    [],
  );

  const onNodeClick = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      setSelectedNodeId(node.id);
    },
    [setSelectedNodeId],
  );

  const onPaneClick = useCallback(() => {
    setSelectedNodeId(null);
  }, [setSelectedNodeId]);

  // 处理拖拽放置
  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      if (!reactFlowBounds) return;

      const type = event.dataTransfer.getData("application/reactflow");
      if (!type) return;

      try {
        const nodeData: DragTransferData = JSON.parse(type);

        // 计算放置位置
        const position = screenToFlowPosition(event.clientX, event.clientY);

        // 添加节点
        addNode({
          label: nodeData.nodeName,
          type: nodeData.nodeType as NodeType,
          position,
          data: { label: nodeData.nodeName, nodeType: nodeData.nodeType },
        });
      } catch (error) {
        console.error("Failed to parse drag data:", error);
      }
    },
    [addNode, screenToFlowPosition],
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  return (
    <div ref={reactFlowWrapper} className="h-full w-full">
      <ReactFlow
        proOptions={{ hideAttribution: true }}
        nodeTypes={nodeTypes}
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={onNodeClick}
        onPaneClick={onPaneClick}
        onNodeDragStart={handleNodeDragStart}
        onNodeDragStop={handleNodeDragStop}
        onDrop={onDrop}
        onDragOver={onDragOver}
      >
        <Background size={2} gap={20} bgColor="#f9f9f9" />
        <CanvasOverlay />
      </ReactFlow>
    </div>
  );
}
