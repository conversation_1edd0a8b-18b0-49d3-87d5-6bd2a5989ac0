import { Button } from "antd";

import { CloseIcon, EraseIcon, RepairIcon } from "@/components/main/icon";
import { useWorkflowStore } from "@/stores/workflowStore";
import ResizablePanel from "../../resizablePanel";

export default function DebugPanel() {
  const debugPanel = useWorkflowStore((state) => state.debugPanel);
  const setDebugPanel = useWorkflowStore((state) => state.setDebugPanel);

  if (!debugPanel.isOpen) return null;

  return (
    <ResizablePanel
      width={debugPanel.width}
      onWidthChange={(width) => setDebugPanel({ width })}
    >
      <div className="flex h-[46px] items-center justify-between border-b border-border-1 bg-bg-light-3 p-3">
        <div className="flex items-center gap-2">
          <div className="text-sm font-medium">调试预览</div>
          <RepairIcon className="text-base text-text-2" />
          <EraseIcon className="text-base text-text-2" />
        </div>
        <Button
          type="text"
          size="small"
          onClick={() => setDebugPanel({ isOpen: false })}
        >
          <CloseIcon />
        </Button>
      </div>
    </ResizablePanel>
  );
}
