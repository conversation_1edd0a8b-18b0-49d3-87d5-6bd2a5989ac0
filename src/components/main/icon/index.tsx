import React, { forwardRef } from "react";

export type IconProps = {
  title?: string;
  style?: React.CSSProperties;
  className?: string;
  svgStyle?: React.CSSProperties;
};

export type SvgIconProps = React.SVGAttributes<SVGSVGElement>;

export const LangflowLogo = ({ title, style, className }: IconProps) => {
  return (
    <span title={title} className={className} style={style}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M13.0486 0.462158H9.75399C9.44371 0.462158 9.14614 0.586082 8.92674 0.806667L4.03751 5.72232C3.81811 5.9429 3.52054 6.06682 3.21026 6.06682H1.16992C0.511975 6.06682 -0.0165756 6.61212 0.000397655 7.2734L0.0515933 9.26798C0.0679586 9.90556 0.586745 10.4139 1.22111 10.4139H3.59097C3.90124 10.4139 4.19881 10.2899 4.41821 10.0694L9.34823 5.11269C9.56763 4.89211 9.8652 4.76818 10.1755 4.76818H13.0486C13.6947 4.76818 14.2185 4.24157 14.2185 3.59195V1.63839C14.2185 0.988773 13.6947 0.462158 13.0486 0.462158Z" />
        <path d="M19.5355 11.5862H22.8301C23.4762 11.5862 24 12.1128 24 12.7624V14.716C24 15.3656 23.4762 15.8922 22.8301 15.8922H19.957C19.6467 15.8922 19.3491 16.0161 19.1297 16.2367L14.1997 21.1934C13.9803 21.414 13.6827 21.5379 13.3725 21.5379H11.0026C10.3682 21.5379 9.84945 21.0296 9.83309 20.392L9.78189 18.3974C9.76492 17.7361 10.2935 17.1908 10.9514 17.1908H12.9918C13.302 17.1908 13.5996 17.0669 13.819 16.8463L18.7082 11.9307C18.9276 11.7101 19.2252 11.5862 19.5355 11.5862Z" />
        <path d="M19.5355 2.9796L22.8301 2.9796C23.4762 2.9796 24 3.50622 24 4.15583V6.1094C24 6.75901 23.4762 7.28563 22.8301 7.28563H19.957C19.6467 7.28563 19.3491 7.40955 19.1297 7.63014L14.1997 12.5868C13.9803 12.8074 13.6827 12.9313 13.3725 12.9313H10.493C10.1913 12.9313 9.90126 13.0485 9.68346 13.2583L4.14867 18.5917C3.93087 18.8016 3.64085 18.9187 3.33917 18.9187H1.32174C0.675616 18.9187 0.151832 18.3921 0.151832 17.7425V15.7343C0.151832 15.0846 0.675616 14.558 1.32174 14.558H3.32468C3.63496 14.558 3.93253 14.4341 4.15193 14.2135L9.40827 8.92878C9.62767 8.70819 9.92524 8.58427 10.2355 8.58427H12.9918C13.302 8.58427 13.5996 8.46034 13.819 8.23976L18.7082 3.32411C18.9276 3.10353 19.2252 2.9796 19.5355 2.9796Z" />
      </svg>
    </span>
  );
};

export const SidebarTriggerIcon = ({
  title,
  style,
  className,
  svgStyle,
}: IconProps) => {
  return (
    <span title={title} className={className} style={style}>
      <svg
        width="1em"
        height="1em"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        style={svgStyle}
      >
        <g id="SlideBar">
          <rect
            id="Rectangle 11"
            x="2.25"
            y="4.5"
            width="19.5"
            height="15"
            rx="3"
            stroke="#7A7A7A"
            strokeWidth="1.5"
          />
          <path
            id="Vector 8"
            d="M10.5 3.75L10.5 20.25"
            stroke="#7A7A7A"
            strokeWidth="1.5"
          />
          <path
            id="Vector 9"
            d="M5.25 7.875H7.5"
            stroke="#7A7A7A"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
          <path
            id="Vector 10"
            d="M5.25 10.875H7.5"
            stroke="#7A7A7A"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
          <path
            id="Vector 11"
            d="M5.25 13.875H7.5"
            stroke="#7A7A7A"
            strokeWidth="1.5"
            strokeLinecap="round"
          />
        </g>
      </svg>
    </span>
  );
};

export const WorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="Reply Arrow">
        <g id="Group">
          <path
            id="&#229;&#189;&#162;&#231;&#138;&#182;&#231;&#187;&#147;&#229;&#144;&#136;"
            fillRule="evenodd"
            clipRule="evenodd"
            d="M4.94498 0.625C3.65171 0.625 2.60331 1.6734 2.60331 2.96667C2.60331 4.25993 3.65171 5.30833 4.94498 5.30833C5.98828 5.30833 6.87222 4.62604 7.17495 3.68332H13.874C15.7567 3.68332 17.249 5.13232 17.249 6.89165C17.249 8.46102 15.9768 9.824 14.3739 10.0629V8.84995C14.3739 7.12406 12.9748 5.72495 11.2489 5.72495H8.74894C7.02305 5.72495 5.62394 7.12406 5.62394 8.84995V9.01264C3.29258 9.21185 1.39453 11.1507 1.39453 13.4541C1.39453 15.9122 3.45452 17.9124 6.01953 17.9124H12.8832C13.2318 18.7679 14.0718 19.3708 15.0525 19.3708C16.3458 19.3708 17.3942 18.3224 17.3942 17.0292C17.3942 15.7359 16.3458 14.6875 15.0525 14.6875C13.884 14.6875 12.9154 15.5434 12.7394 16.6624H6.01953C4.13678 16.6624 2.64453 15.2134 2.64453 13.4541C2.64453 11.8508 3.9723 10.463 5.62394 10.2689V11.3499C5.62394 13.0758 7.02305 14.4749 8.74894 14.4749H11.2489C12.9748 14.4749 14.3739 13.0758 14.3739 11.3499V11.323C16.6573 11.0758 18.499 9.16056 18.499 6.89165C18.499 4.4336 16.439 2.43332 13.874 2.43332H7.22564C6.98424 1.39703 6.05477 0.625 4.94498 0.625ZM15.0525 15.9375C15.6554 15.9375 16.1442 16.4263 16.1442 17.0292C16.1442 17.6321 15.6554 18.1208 15.0525 18.1208C14.4496 18.1208 13.9609 17.6321 13.9609 17.0292C13.9609 16.4263 14.4496 15.9375 15.0525 15.9375ZM11.2489 6.97479C12.2845 6.97479 13.1239 7.81426 13.1239 8.84979V11.3498C13.1239 12.3853 12.2845 13.2248 11.2489 13.2248H8.74894C7.71341 13.2248 6.87394 12.3853 6.87394 11.3498V8.84979C6.87394 7.81426 7.71341 6.97479 8.74894 6.97479H11.2489ZM11.5614 9.47495C11.9066 9.47495 12.1864 9.75477 12.1864 10.0999C12.1864 10.4451 11.9066 10.7249 11.5614 10.7249H10.6239V11.6624C10.6239 12.0076 10.3441 12.2874 9.99894 12.2874C9.65376 12.2874 9.37394 12.0076 9.37394 11.6624V10.7249H8.43644C8.09126 10.7249 7.81144 10.4451 7.81144 10.0999C7.81144 9.75477 8.09126 9.47495 8.43644 9.47495H9.37394V8.53745C9.37394 8.19227 9.65376 7.91245 9.99894 7.91245C10.3441 7.91245 10.6239 8.19227 10.6239 8.53745V9.47495H11.5614Z"
            fill="currentColor"
          />
        </g>
      </g>
    </svg>
  );
};

export const KnowledgeIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="Book Open">
        <path
          id="&#229;&#189;&#162;&#231;&#138;&#182;&#231;&#187;&#147;&#229;&#144;&#136;"
          d="M5.7627 2.36719C6.34924 2.38778 6.96064 2.48414 7.56738 2.66699C8.63152 2.98 9.49536 3.48472 10.2285 4.09961H15.9902C17.2775 4.09961 18.3317 5.14671 18.332 6.43262V15C18.3318 16.2867 17.277 17.3408 15.9902 17.3408H10.0029C10.0017 17.3408 10.0002 17.3418 9.99902 17.3418H9.99609C9.98763 17.3418 9.97917 17.3421 9.9707 17.3418H4.00781C2.72098 17.3418 1.66603 16.2868 1.66602 15V6.43359C1.66602 5.14675 2.72097 4.0918 4.00781 4.0918H4.24121V3.84961C4.2414 3.05727 4.85545 2.40964 5.63184 2.36816L5.69727 2.36621L5.7627 2.36719ZM4.00781 5.3418C3.41132 5.3418 2.91602 5.83711 2.91602 6.43359V15C2.91603 15.5965 3.41133 16.0918 4.00781 16.0918H8.69434C8.30653 15.7224 7.83558 15.3999 7.25586 15.1621C6.73326 14.9492 6.19449 14.8436 5.65137 14.8164L5.58398 14.8125C4.82396 14.7498 4.24128 14.1072 4.24121 13.3418V5.3418H4.00781ZM10.625 16.0908H15.9902C16.5866 16.0908 17.0818 15.5963 17.082 15V6.43262C17.0817 5.83895 16.589 5.34961 15.9902 5.34961H10.625V16.0908ZM5.68945 3.61719C5.57918 3.62912 5.49138 3.72581 5.49121 3.84961V13.3418C5.49128 13.4643 5.58427 13.5629 5.70605 13.5674C6.38956 13.6016 7.06366 13.734 7.72852 14.0049C8.36668 14.2666 8.90151 14.6046 9.35156 14.9932L9.37402 15.0137V5.0127L9.25098 4.91406C8.71654 4.49646 8.1004 4.15002 7.36914 3.91406L7.21094 3.86523C6.70631 3.71316 6.20126 3.63329 5.71973 3.61621L5.68945 3.61719ZM15.1494 13.5498C15.4945 13.5498 15.7743 13.8297 15.7744 14.1748C15.7744 14.52 15.4946 14.7998 15.1494 14.7998H12.5576C12.2125 14.7998 11.9326 14.52 11.9326 14.1748C11.9327 13.8297 12.2125 13.5498 12.5576 13.5498H15.1494ZM15.1494 10.0918C15.4946 10.0918 15.7744 10.3716 15.7744 10.7168C15.7743 11.0619 15.4945 11.3418 15.1494 11.3418H12.5576C12.2125 11.3418 11.9327 11.0619 11.9326 10.7168C11.9326 10.3716 12.2125 10.0918 12.5576 10.0918H15.1494ZM15.1494 6.6416C15.4946 6.64164 15.7744 6.92145 15.7744 7.2666C15.7744 7.61176 15.4946 7.89156 15.1494 7.8916H12.5576C12.2125 7.89156 11.9326 7.61176 11.9326 7.2666C11.9326 6.92145 12.2125 6.64164 12.5576 6.6416H15.1494Z"
          fill="currentColor"
        />
      </g>
    </svg>
  );
};

export const CloseIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="Close">
        <path
          id="&#232;&#183;&#175;&#229;&#190;&#132;"
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.7725 3.22701C12.9677 3.42228 12.9677 3.73886 12.7725 3.93412L8.7066 7.99998L12.7725 12.0658C12.9677 12.2611 12.9677 12.5777 12.7725 12.773C12.5772 12.9682 12.2606 12.9682 12.0654 12.773L7.9995 8.70709L3.93363 12.773C3.73837 12.9682 3.42179 12.9682 3.22652 12.773C3.03126 12.5777 3.03126 12.2611 3.22652 12.0658L7.29239 7.99998L3.22652 3.93412C3.03126 3.73886 3.03126 3.42228 3.22652 3.22701C3.42179 3.03175 3.73837 3.03175 3.93363 3.22701L7.9995 7.29288L12.0654 3.22701C12.2606 3.03175 12.5772 3.03175 12.7725 3.22701Z"
          fill="currentColor"
        />
      </g>
    </svg>
  );
};

export const CheckPassIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 8 6"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M0.396522 3.34623C0.20126 3.15097 0.20126 2.83438 0.396522 2.63912C0.591784 2.44386 0.908367 2.44386 1.10363 2.63912L3.22495 4.76044L7.11404 0.871355C7.3093 0.676093 7.62588 0.676093 7.82114 0.871355C8.01641 1.06662 8.01641 1.3832 7.82114 1.57846L3.5785 5.8211C3.38324 6.01637 3.06666 6.01636 2.8714 5.8211L0.396522 3.34623Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const CircleOutlineCloseIcon = (props: SvgIconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 25"
      fill="none"
      {...props}
    >
      <circle cx="12" cy="12.5001" r="8.83333" stroke="currentColor" />
      <path
        d="M8.22876 8.72876L15.7712 16.2712"
        stroke="currentColor"
        strokeLinecap="round"
      />
      <path
        d="M15.7712 8.72876L8.22877 16.2712"
        stroke="currentColor"
        strokeLinecap="round"
      />
    </svg>
  );
};

export const SquarePlusIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="PlusSquare">
        <rect
          id="Rectangle 11"
          x="2.25"
          y="2.25"
          width="11.5"
          height="11.5"
          rx="2.5"
          stroke="currentColor"
        />
        <path
          id="Vector 6"
          d="M5 8H11"
          stroke="currentColor"
          strokeLinecap="round"
        />
        <path
          id="Vector 7"
          d="M8 5L8 11"
          stroke="currentColor"
          strokeLinecap="round"
        />
      </g>
    </svg>
  );
};

export const ClockIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="Clock">
        <path
          id="&#229;&#189;&#162;&#231;&#138;&#182;&#231;&#187;&#147;&#229;&#144;&#136;"
          d="M8 1.5C11.5899 1.5 14.5 4.41015 14.5 8C14.5 11.5899 11.5899 14.5 8 14.5C4.41015 14.5 1.5 11.5899 1.5 8C1.5 4.41015 4.41015 1.5 8 1.5ZM8 2.5C4.96243 2.5 2.5 4.96243 2.5 8C2.5 11.0376 4.96243 13.5 8 13.5C11.0376 13.5 13.5 11.0376 13.5 8C13.5 4.96243 11.0376 2.5 8 2.5ZM7.75 4.33887C8.01359 4.33887 8.22994 4.54304 8.24902 4.80176L8.25 4.83887V8.43652L10.1455 10.3477C10.3297 10.5334 10.3382 10.8277 10.1719 11.0234L10.1426 11.0547C9.95683 11.2389 9.66256 11.2474 9.4668 11.0811L9.43555 11.0518L7.46777 9.06738C7.34125 8.93978 7.26482 8.7718 7.25195 8.59375L7.25 8.54004V4.83887C7.25 4.56272 7.47386 4.33887 7.75 4.33887Z"
          fill="currentColor"
        />
      </g>
    </svg>
  );
};

export const EditIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="Edit">
        <path
          id="Rectangle 17"
          d="M10.0763 3.51755C10.6621 2.93177 11.6118 2.93177 12.1976 3.51755C12.7834 4.10334 12.7834 5.05309 12.1976 5.63887L5.04435 12.7921L2.97137 13.3584C2.62163 13.4537 2.2985 13.1588 2.34298 12.8142L2.35679 12.7438L2.92303 10.6708L10.0763 3.51755Z"
          stroke="currentColor"
          strokeLinejoin="round"
        />
        <path
          id="Vector 12"
          d="M7.5 13.75L13.5 13.75"
          stroke="currentColor"
          strokeLinecap="round"
        />
      </g>
    </svg>
  );
};

export const CircleCloseIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="Close">
        <path
          id="01_PC/01_Basic/22_close-circle"
          d="M12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12C2.25 6.61522 6.61522 2.25 12 2.25ZM16.1016 8.06152C15.8087 7.76902 15.3338 7.76887 15.041 8.06152L11.999 11.1025L8.95801 8.06152C8.66512 7.76903 8.19024 7.76886 7.89746 8.06152C7.60457 8.35442 7.60457 8.83015 7.89746 9.12305L10.9385 12.1641L7.89746 15.2051C7.60457 15.498 7.60457 15.9737 7.89746 16.2666C8.19024 16.5592 8.66513 16.5591 8.95801 16.2666L11.999 13.2246L15.041 16.2666C15.3338 16.5592 15.8087 16.5591 16.1016 16.2666C16.3945 15.9737 16.3945 15.498 16.1016 15.2051L13.0605 12.1641L16.1016 9.12305C16.3945 8.83015 16.3945 8.35442 16.1016 8.06152Z"
          fill="currentColor"
        />
      </g>
    </svg>
  );
};

export const SearchIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="Search">
        <path
          id="&#229;&#189;&#162;&#231;&#138;&#182;&#231;&#187;&#147;&#229;&#144;&#136;"
          d="M12.0283 12.5283C12.2373 12.3197 12.5691 12.3085 12.791 12.4951L12.8271 12.5283L14.334 14.0352C14.5547 14.2558 14.5547 14.6143 14.334 14.835C14.1248 15.0436 13.7932 15.0541 13.5713 14.8672L13.5352 14.835L12.0283 13.3281C11.8076 13.1075 11.8076 12.749 12.0283 12.5283ZM7.54785 1.25C10.9176 1.25 13.5967 3.98933 13.5967 7.43555C13.5967 10.8818 10.9175 13.6211 7.54785 13.6211C4.17831 13.6209 1.50001 10.8816 1.5 7.43555C1.5 3.98944 4.17831 1.25018 7.54785 1.25ZM7.54785 2.37988C4.80842 2.38003 2.63086 4.6079 2.63086 7.43555C2.63088 10.2632 4.80843 12.4911 7.54785 12.4912C10.2874 12.4912 12.4658 10.2633 12.4658 7.43555C12.4658 4.60781 10.2874 2.37988 7.54785 2.37988Z"
          fill="currentColor"
        />
      </g>
    </svg>
  );
};

export const LeftArrowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.9872 13.8296C11.1824 13.6343 11.1824 13.3177 10.9872 13.1225L5.87721 7.99855L10.9872 2.92924C11.1824 2.73397 11.1824 2.41739 10.9872 2.22213C10.7919 2.02687 10.4753 2.02687 10.2801 2.22213L4.81637 7.64482C4.62111 7.84008 4.62111 8.15666 4.81637 8.35193L10.2801 13.8296C10.4753 14.0248 10.7919 14.0248 10.9872 13.8296Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const RightArrowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.81637 13.8296C4.62111 13.6343 4.62111 13.3177 4.81637 13.1225L9.92634 7.99855L4.81637 2.92924C4.62111 2.73397 4.62111 2.41739 4.81637 2.22213C5.01163 2.02687 5.32821 2.02687 5.52348 2.22213L10.9872 7.64482C11.1824 7.84008 11.1824 8.15667 10.9872 8.35193L5.52348 13.8296C5.32821 14.0248 5.01163 14.0248 4.81637 13.8296Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const DownArrowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_5055_9634)">
        <path
          d="M8 3.33334V12.6667"
          stroke="currentColor"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12 8.66666L8 12.6667"
          stroke="currentColor"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M4 8.66666L8 12.6667"
          stroke="currentColor"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_5055_9634">
          <rect width="1em" height="1em" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const DocIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.8584 1.32821C9.97885 1.32973 10.0946 1.37503 10.1846 1.45516L13.083 4.03719C13.1892 4.13203 13.25 4.26783 13.25 4.41024V13.5841C13.2498 14.2282 12.7272 14.7501 12.083 14.7501H3.91699C3.27279 14.7501 2.75021 14.2282 2.75 13.5841V2.41122C2.75762 1.76697 3.2864 1.25032 3.93066 1.25789L9.8584 1.32821ZM3.91895 2.25789C3.82693 2.25681 3.7511 2.33095 3.75 2.41707V13.5831C3.75 13.6751 3.82494 13.7501 3.91699 13.7501H12.083C12.1751 13.7501 12.25 13.6751 12.25 13.5831V5.31942H10.3701C9.75417 5.31941 9.24762 4.84187 9.20605 4.23348L9.2041 4.17879L9.16211 2.31942L3.91895 2.25789ZM9 9.87508C9.27614 9.87508 9.5 10.0989 9.5 10.3751C9.5 10.6387 9.29582 10.855 9.03711 10.8741L9 10.8751H5.5C5.22386 10.8751 5 10.6512 5 10.3751C5 10.1115 5.20418 9.89515 5.46289 9.87606L5.5 9.87508H9ZM10.3906 6.62508C10.6668 6.62508 10.8906 6.84894 10.8906 7.12508C10.8906 7.38867 10.6864 7.60502 10.4277 7.62411L10.3906 7.62508H5.35938C5.08323 7.62508 4.85938 7.40122 4.85938 7.12508C4.85938 6.86149 5.06355 6.64515 5.32227 6.62606L5.35938 6.62508H10.3906ZM10.2031 4.15633C10.205 4.23863 10.2666 4.30684 10.3457 4.31844L10.3701 4.31942H11.8975L10.1719 2.78231L10.2031 4.15633Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const CircleInfoIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 18.125C5.51297 18.125 1.875 14.487 1.875 10C1.875 5.51195 5.51297 1.875 10 1.875C14.487 1.875 18.125 5.51195 18.125 10C18.125 14.487 14.487 18.125 10 18.125ZM10.0001 7.21307C10.449 7.21307 10.8126 6.84948 10.8126 6.40057C10.8126 5.95167 10.449 5.58807 10.0001 5.58807C9.55122 5.58807 9.18762 5.95167 9.18762 6.40057C9.18762 6.84948 9.55122 7.21307 10.0001 7.21307ZM10 14.5312C10.3301 14.5312 10.5995 14.2832 10.6233 13.9674L10.625 13.9219V9.04688C10.625 8.7107 10.3458 8.4375 10 8.4375C9.67088 8.4375 9.40061 8.68643 9.37672 9.00144L9.375 9.04688V13.9219C9.375 14.2591 9.65521 14.5312 10 14.5312Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const RestoreIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M13.1069 6.68531C13.3625 6.64484 13.6036 6.8056 13.6684 7.05055L13.6791 7.10035L13.6957 7.21363C13.8564 8.37676 13.6573 9.56226 13.1235 10.6101C12.5722 11.692 11.6917 12.5725 10.6098 13.1238C9.52803 13.6748 8.29914 13.8694 7.10004 13.6795C5.93833 13.4954 4.86175 12.9591 4.01508 12.1453L3.99945 12.1296V12.9998L3.9975 13.0515C3.97178 13.3035 3.75825 13.4998 3.49945 13.4998C3.2408 13.4996 3.02811 13.3034 3.00238 13.0515L2.99945 12.9998V10.9998C2.99964 10.7239 3.22357 10.4999 3.49945 10.4998H5.49945C5.77548 10.4998 5.99926 10.7238 5.99945 10.9998C5.99945 11.2759 5.77559 11.4998 5.49945 11.4998H4.78949C5.47596 12.1295 6.33324 12.5449 7.25629 12.6912C8.24704 12.8481 9.26291 12.6876 10.1567 12.2322C11.0501 11.7768 11.7765 11.0504 12.2319 10.157C12.6588 9.3191 12.8272 8.37424 12.7172 7.44313L12.6909 7.2566L12.686 7.20582C12.672 6.95299 12.8514 6.72588 13.1069 6.68531ZM5.3891 2.87672C6.47097 2.3255 7.69961 2.13115 8.89887 2.32106C9.95593 2.48848 10.9424 2.94704 11.7495 3.64137V2.99977L11.7524 2.94898C11.778 2.69696 11.9907 2.49992 12.2495 2.49977C12.5083 2.49977 12.7219 2.69685 12.7475 2.94898L12.7495 2.99977V4.99977C12.7495 5.27591 12.5256 5.49977 12.2495 5.49977H10.2495C9.97345 5.4996 9.74945 5.27581 9.74945 4.99977C9.74964 4.72389 9.97357 4.49993 10.2495 4.49977H11.2094C10.5229 3.87023 9.66556 3.45456 8.74262 3.30836C7.75191 3.15148 6.73693 3.31295 5.8432 3.76832C4.94952 4.22371 4.22241 4.94982 3.76703 5.84352C3.34011 6.68143 3.17267 7.62625 3.28266 8.55738L3.30805 8.74293L3.31391 8.79371C3.32806 9.04675 3.14773 9.2747 2.89203 9.3152C2.63653 9.35547 2.39527 9.19476 2.33051 8.94996L2.32074 8.89918L2.30414 8.78688C2.14341 7.62348 2.34241 6.43748 2.8764 5.38941C3.42768 4.30764 4.3073 3.42794 5.3891 2.87672Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const CircleCheckIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12 2.25C17.3848 2.25 21.75 6.61522 21.75 12C21.75 17.3848 17.3848 21.75 12 21.75C6.61522 21.75 2.25 17.3848 2.25 12C2.25 6.61522 6.61522 2.25 12 2.25ZM12 3.75C7.44365 3.75 3.75 7.44365 3.75 12C3.75 16.5563 7.44365 20.25 12 20.25C16.5563 20.25 20.25 16.5563 20.25 12C20.25 7.44365 16.5563 3.75 12 3.75ZM16.1201 8.53906C16.4094 8.24264 16.8842 8.23622 17.1807 8.52539C17.4615 8.7994 17.4818 9.2404 17.2373 9.53809L17.1934 9.58594L11.4619 15.46C11.1849 15.7435 10.7385 15.7602 10.4414 15.5088L10.3936 15.4648L7.05078 12.1006C6.75895 11.8068 6.75998 11.332 7.05371 11.04C7.33204 10.7635 7.77351 10.7509 8.06738 11L8.11426 11.0439L10.9199 13.8672L16.1201 8.53906Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const CircleCheckFilledIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10 1.875C14.4873 1.875 18.125 5.51269 18.125 10C18.125 14.4873 14.4873 18.125 10 18.125C5.51269 18.125 1.875 14.4873 1.875 10C1.875 5.51269 5.51269 1.875 10 1.875ZM14.3105 7.27832C14.0697 7.04367 13.6842 7.04847 13.4492 7.28906L9.22461 11.6182L6.94434 9.32422L6.90625 9.28906C6.66745 9.08661 6.30915 9.09751 6.08301 9.32227C5.8444 9.55947 5.84298 9.9449 6.08008 10.1836L8.7959 12.917L8.83496 12.9531C9.07641 13.1575 9.43995 13.1437 9.66504 12.9131L14.3213 8.14062L14.3564 8.10156C14.5552 7.85969 14.5387 7.50099 14.3105 7.27832Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const AttachmentIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.34134 2.81247C8.74849 1.3981 11.0358 1.3981 12.4483 2.81066C13.8413 4.19649 13.8624 6.43599 12.5125 7.85372L12.4492 7.91856L6.86857 13.4992C5.86542 14.5024 4.2438 14.5024 3.24065 13.4992C2.25573 12.5143 2.23782 10.9332 3.18692 9.9266L3.24065 9.87128L8.83373 4.27819C9.43294 3.67899 10.402 3.67899 11.0013 4.27819C11.5851 4.86204 11.6001 5.79705 11.0462 6.3989L11.0013 6.44571L6.86857 10.5784C6.67331 10.7736 6.35673 10.7736 6.16147 10.5784C5.97648 10.3934 5.96675 10.0995 6.13226 9.90308L6.16147 9.87128L10.2941 5.7386C10.5028 5.52992 10.5028 5.19398 10.2941 4.9853C10.0959 4.78705 9.7828 4.77714 9.57306 4.95556L9.54084 4.9853L3.94775 10.5784C3.33512 11.191 3.33512 12.1795 3.94775 12.7921C4.54506 13.3894 5.49965 13.4044 6.11483 12.8369L6.16147 12.7921L11.7421 7.21146C12.7648 6.18882 12.7648 4.53609 11.7421 3.51867C10.7378 2.51429 9.1256 2.49636 8.10446 3.46486L8.04935 3.51867L3.92911 7.63891C3.73385 7.83417 3.41726 7.83417 3.222 7.63891C3.03702 7.45392 3.02728 7.16005 3.19279 6.96361L3.222 6.9318L7.34134 2.81247Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const DeleteIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.5 1.75C9.88071 1.75 11 2.86929 11 4.25V4.75H13.75C14.0261 4.75 14.25 4.97386 14.25 5.25C14.25 5.52614 14.0261 5.75 13.75 5.75H12.5V11.75C12.5 13.1307 11.3807 14.25 10 14.25H6C4.61929 14.25 3.5 13.1307 3.5 11.75V5.75H2.25C1.97386 5.75 1.75 5.52614 1.75 5.25C1.75 4.97386 1.97386 4.75 2.25 4.75H5V4.25C5 2.86929 6.11929 1.75 7.5 1.75H8.5ZM4.5 11.75C4.5 12.5784 5.17157 13.25 6 13.25H10C10.8284 13.25 11.5 12.5784 11.5 11.75V5.75H4.5V11.75ZM6.75 7.5C7.02614 7.5 7.25 7.72386 7.25 8V11C7.25 11.2761 7.02614 11.5 6.75 11.5C6.47386 11.5 6.25 11.2761 6.25 11V8C6.25 7.72386 6.47386 7.5 6.75 7.5ZM9.25 7.5C9.52614 7.5 9.75 7.72386 9.75 8V11C9.75 11.2761 9.52614 11.5 9.25 11.5C8.97386 11.5 8.75 11.2761 8.75 11V8C8.75 7.72386 8.97386 7.5 9.25 7.5ZM7.5 2.75C6.67157 2.75 6 3.42157 6 4.25V4.75H10V4.25C10 3.42157 9.32843 2.75 8.5 2.75H7.5Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const TriangleAiWorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M4 4.19111C4.00008 3.05027 5.22325 2.32709 6.2229 2.8769L13.1479 6.68574C14.1839 7.25566 14.1839 8.74424 13.1479 9.31416L6.2229 13.123C5.22325 13.6728 4.00008 12.9496 4 11.8088L4 4.19111ZM5 11.8088C5.00008 12.189 5.40777 12.43 5.74097 12.2468L12.666 8.43794C13.0112 8.24794 13.0112 7.75195 12.666 7.56196L5.74097 3.75312C5.40777 3.56986 5.00008 3.81087 5 4.19111L5 11.8088Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const PlusSquareAiWorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M45 7C51.6274 7 57 12.3726 57 19V45C57 51.6274 51.6274 57 45 57H19L18.6904 56.9961C12.2061 56.8319 7 51.5239 7 45V19C7 12.3726 12.3726 7 19 7H45ZM19 11C14.5817 11 11 14.5817 11 19V45C11 49.4183 14.5817 53 19 53H45C49.4183 53 53 49.4183 53 45V19C53 14.5817 49.4183 11 45 11H19ZM32 18C33.1046 18 34 18.8954 34 20V30H44C45.1046 30 46 30.8954 46 32C46 33.1046 45.1046 34 44 34H34V44C34 45.1046 33.1046 46 32 46C30.8954 46 30 45.1046 30 44V34H20C18.8954 34 18 33.1046 18 32C18 30.8954 18.8954 30 20 30H30V20C30 18.8954 30.8954 18 32 18Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const SlideBarAiWorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M50 10C55.5229 10 60 14.4772 60 20V44C60 49.5228 55.5228 54 50 54H14L13.4854 53.9873C8.20167 53.7195 4 49.3502 4 44V20C4 14.4772 8.47715 10 14 10H50ZM14 14C10.6863 14 8 16.6863 8 20V44C8 47.3137 10.6863 50 14 50H26V14H14ZM30 50H50C53.3137 50 56 47.3137 56 44V20C56 16.6863 53.3137 14 50 14H30V50ZM20 35C21.1046 35 22 35.8954 22 37C22 38.1046 21.1046 39 20 39H14C12.8954 39 12 38.1046 12 37C12 35.8954 12.8954 35 14 35H20ZM20 27C21.1046 27 22 27.8954 22 29C22 30.1046 21.1046 31 20 31H14C12.8954 31 12 30.1046 12 29C12 27.8954 12.8954 27 14 27H20ZM20 19C21.1046 19 22 19.8954 22 21C22 22.1046 21.1046 23 20 23H14C12.8954 23 12 22.1046 12 21C12 19.8954 12.8954 19 14 19H20Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const CopyAiWorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M37 7C42.5228 7 47 11.4772 47 17C52.5228 17 57 21.4772 57 27V47C57 52.5228 52.5228 57 47 57H27C21.4772 57 17 52.5228 17 47C11.6498 47 7.28054 42.7983 7.0127 37.5146L7 37V17C7 11.4772 11.4772 7 17 7H37ZM27 21C23.6863 21 21 23.6863 21 27V47C21 50.3137 23.6863 53 27 53H47C50.3137 53 53 50.3137 53 47V27C53 23.6863 50.3137 21 47 21H27ZM17 11C13.6863 11 11 13.6863 11 17V37L11.0078 37.3086C11.1684 40.4789 13.7898 43 17 43V27C17 21.4772 21.4772 17 27 17H43C43 13.6863 40.3137 11 37 11H17Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const EditAiWorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M54.2039 53.0103C55.2124 53.1126 55.9995 53.9641 55.9998 54.9996C55.9998 56.0352 55.2125 56.8866 54.2039 56.9888L53.9998 56.9996H29.9998C28.8953 56.9995 27.9998 56.1041 27.9998 54.9996C28 53.8953 28.8954 52.9997 29.9998 52.9996H53.9998L54.2039 53.0103ZM39.1902 12.3726C42.3306 9.53594 47.1785 9.63049 50.2048 12.6568C53.3288 15.7809 53.3286 20.8461 50.2048 23.9703L21.2126 52.9615L12.4099 55.3619C9.51719 56.1507 6.85533 53.6048 7.43237 50.73L7.49878 50.4507L9.89917 41.648L38.8904 12.6568L39.1902 12.3726ZM13.4783 43.7261L11.3582 51.5035L19.1355 49.3834L21.2615 47.2564L15.6042 41.6002L13.4783 43.7261ZM47.3757 15.4849C45.8137 13.9233 43.2815 13.9233 41.7195 15.4849L18.4333 38.7711L24.0896 44.4283L47.3757 21.1422C48.9378 19.5801 48.9378 17.047 47.3757 15.4849Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const RestoreAiWorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M52.429 26.7393C53.4515 26.5776 54.416 27.2215 54.675 28.2013L54.717 28.4024L54.7834 28.8526C55.4263 33.5062 54.6294 38.2492 52.4934 42.4415C50.2883 46.7692 46.7693 50.2882 42.4417 52.4933C38.1142 54.6981 33.1996 55.4765 28.4026 54.7169C23.7553 53.9808 19.4488 51.8377 16.0618 48.5821L16.0002 48.5216V52.0001L15.9895 52.2042C15.8872 53.2127 15.0357 53.9999 14.0002 54.0001C12.9646 54.0001 12.1133 53.2128 12.011 52.2042L12.0002 52.0001V44.0001C12.0002 42.8955 12.8957 42.0001 14.0002 42.0001H22.0002C23.1047 42.0003 24.0002 42.8956 24.0002 44.0001C24.0002 45.1045 23.1046 45.9999 22.0002 46.0001H19.1575C21.9038 48.5194 25.3348 50.1807 29.0276 50.7657C32.9905 51.3933 37.0512 50.7504 40.6262 48.9288C44.201 47.1072 47.1074 44.2009 48.929 40.6261C50.6367 37.2744 51.3093 33.4962 50.8694 29.7716L50.7659 29.0274L50.7444 28.8243C50.6878 27.8122 51.4062 26.9013 52.429 26.7393ZM21.5588 11.5069C25.8863 9.30214 30.8009 8.5236 35.5979 9.28329C39.8263 9.9531 43.772 11.788 47.0002 14.5655V12.0001L47.011 11.796C47.1132 10.7873 47.9646 10.0001 49.0002 10.0001C50.0357 10.0003 50.8872 10.7874 50.9895 11.796L51.0002 12.0001V20.0001C51.0002 21.1045 50.1046 21.9999 49.0002 22.0001H41.0002C39.8957 22.0001 39.0003 21.1046 39.0002 20.0001C39.0002 18.8955 39.8957 18.0001 41.0002 18.0001H44.843C42.0968 15.4807 38.6657 13.8195 34.9729 13.2345C31.01 12.6068 26.9493 13.2499 23.3743 15.0714C19.7995 16.8929 16.8931 19.7993 15.0715 23.3741C13.3637 26.7258 12.6912 30.504 13.1311 34.2286L13.2346 34.9727L13.2561 35.1759C13.3127 36.1878 12.5941 37.0987 11.5715 37.2608C10.549 37.4227 9.58459 36.7786 9.32544 35.7989L9.28345 35.5977L9.21704 35.1476C8.57411 30.4939 9.37108 25.751 11.5071 21.5587C13.7122 17.231 17.2311 13.712 21.5588 11.5069Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const CodeAiWorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M14.2484 19.2721C15.0294 18.491 16.2958 18.491 17.0768 19.2721C17.8579 20.0531 17.8579 21.3195 17.0768 22.1005L7.17732 32L17.0768 41.8995L17.2135 42.0514C17.8545 42.837 17.8091 43.9956 17.0768 44.7279C16.3445 45.4602 15.1859 45.5056 14.4003 44.8646L14.2484 44.7279L2.93468 33.4142C2.15363 32.6332 2.15363 31.3668 2.93468 30.5858L14.2484 19.2721Z"
        fill="currentColor"
      />
      <path
        d="M49.7516 19.2721C48.9706 18.491 47.7042 18.491 46.9232 19.2721C46.1421 20.0531 46.1421 21.3195 46.9232 22.1005L56.8227 32L46.9232 41.8995L46.7865 42.0514C46.1455 42.837 46.1909 43.9956 46.9232 44.7279C47.6555 45.4602 48.8141 45.5056 49.5997 44.8646L49.7516 44.7279L61.0653 33.4142C61.8464 32.6332 61.8464 31.3668 61.0653 30.5858L49.7516 19.2721Z"
        fill="currentColor"
      />
      <path
        d="M24.8918 50.8009L35.2446 12.1638C35.5305 11.0969 36.6271 10.4637 37.6941 10.7496C38.761 11.0355 39.3942 12.1322 39.1083 13.1991L28.7555 51.8362C28.4696 52.9031 27.373 53.5363 26.306 53.2504C25.2391 52.9645 24.6059 51.8678 24.8918 50.8009Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const WorkFlowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 4 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M4 12L3.98926 12.2041C3.88699 13.2128 3.03565 14 2 14C0.964349 14 0.113004 13.2128 0.0107417 12.2041L-5.24537e-07 12L-8.74228e-08 2C-3.91405e-08 0.89543 0.895431 -1.35705e-07 2 -8.74228e-08C3.10457 -3.91405e-08 4 0.89543 4 2L4 12Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const DeleteAiWorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M34 7C39.5229 7 44 11.4772 44 17V19H55C56.1046 19 57 19.8954 57 21C57 22.1046 56.1046 23 55 23H50V47C50 52.5229 45.5228 57 40 57H24C18.4772 57 14 52.5229 14 47V23H9C7.89543 23 7 22.1046 7 21C7 19.8954 7.89543 19 9 19H20V17C20 11.4772 24.4772 7 30 7H34ZM18 47C18 50.3137 20.6863 53 24 53H40C43.3137 53 46 50.3137 46 47V23H18V47ZM27 30C28.1046 30 29 30.8954 29 32V44C29 45.1046 28.1046 46 27 46C25.8954 46 25 45.1046 25 44V32C25 30.8954 25.8954 30 27 30ZM37 30C38.1046 30 39 30.8954 39 32V44C39 45.1046 38.1046 46 37 46C35.8954 46 35 45.1046 35 44V32C35 30.8954 35.8954 30 37 30ZM30 11C26.6863 11 24 13.6863 24 17V19H40V17C40 13.6863 37.3137 11 34 11H30Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const PreviewAiWorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17.0986 2.83838C17.4281 2.83839 17.6978 3.09318 17.7217 3.4165L17.7236 3.46338V7.69775C17.7235 8.04282 17.4437 8.32274 17.0986 8.32275C16.7693 8.32275 16.4997 8.0678 16.4756 7.74463L16.4736 7.69775V4.08838H3.57129V16.9731H7.1875C7.51693 16.9732 7.78669 17.2289 7.81055 17.5522L7.8125 17.5981C7.8125 17.9275 7.55761 18.1981 7.23438 18.2222L7.1875 18.2231H2.94629C2.6168 18.2231 2.34612 17.9684 2.32227 17.645L2.32129 17.5981V3.46338C2.32129 3.13389 2.57602 2.86419 2.89941 2.84033L2.94629 2.83838H17.0986ZM12.5 9.67627C14.3405 9.67656 15.8326 11.1688 15.833 13.0093C15.833 13.7037 15.6191 14.3484 15.2559 14.8823L16.6914 16.3179C16.9355 16.5619 16.9355 16.9576 16.6914 17.2017C16.4602 17.4328 16.0932 17.4455 15.8477 17.2388L15.8076 17.2017L14.3721 15.7661C13.8383 16.1297 13.1944 16.3432 12.5 16.3433C10.6591 16.3433 9.16699 14.8502 9.16699 13.0093C9.1674 11.1687 10.6593 9.67627 12.5 9.67627ZM12.5 10.9263C11.3497 10.9263 10.4174 11.859 10.417 13.0093C10.417 14.1599 11.3494 15.0933 12.5 15.0933C13.6503 15.093 14.583 14.1597 14.583 13.0093C14.5826 11.8592 13.6501 10.9266 12.5 10.9263ZM8.2207 14.0884C8.47959 14.0884 8.68945 14.2982 8.68945 14.5571C8.6894 14.8007 8.50329 15.0012 8.26562 15.0239L8.2207 15.0259H5.7207C5.46191 15.0258 5.25201 14.8159 5.25195 14.5571C5.25195 14.3135 5.43808 14.113 5.67578 14.0903L5.7207 14.0884H8.2207ZM8.2207 10.0415C8.47959 10.0415 8.68945 10.2514 8.68945 10.5103C8.68933 10.7538 8.50325 10.9543 8.26562 10.9771L8.2207 10.979H5.7207C5.46196 10.9789 5.25209 10.769 5.25195 10.5103C5.25195 10.2666 5.43808 10.0662 5.67578 10.0435L5.7207 10.0415H8.2207ZM14.292 6.2915C14.5509 6.2915 14.7607 6.50137 14.7607 6.76025C14.7606 7.00365 14.5753 7.20415 14.3379 7.22705L14.292 7.229H5.7207C5.46196 7.22894 5.25209 7.01898 5.25195 6.76025C5.25195 6.5166 5.43808 6.31617 5.67578 6.29346L5.7207 6.2915H14.292Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const EyeAiWorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 18 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9 2.60376C11.1574 2.60376 12.9062 4.35264 12.9062 6.51001C12.9062 8.66738 11.1574 10.4163 9 10.4163C6.84263 10.4163 5.09375 8.66738 5.09375 6.51001C5.09375 4.35264 6.84263 2.60376 9 2.60376ZM9 3.89575C7.5528 3.89576 6.38087 5.06699 6.38086 6.51001C6.38086 7.95305 7.55279 9.12426 9 9.12427C10.4472 9.12427 11.6191 7.95305 11.6191 6.51001C11.6191 5.06698 10.4472 3.89575 9 3.89575Z"
        fill="currentColor"
      />
      <path
        d="M8.99997 0.26001C11.7668 0.260023 14.3317 1.52353 16.1299 3.68774C17.4565 5.28869 17.4567 7.73076 16.1299 9.33228C14.3382 11.4904 11.7639 12.76 8.99997 12.76C6.23318 12.76 3.66829 11.4965 1.87009 9.33228C0.543371 7.73132 0.543238 5.28926 1.87009 3.68774C3.66173 1.52959 6.23603 0.26001 8.99997 0.26001ZM8.99997 1.51001C6.61694 1.51001 4.39117 2.60747 2.832 4.4856C1.8888 5.62407 1.88915 7.39668 2.832 8.53442C4.39751 10.4184 6.61395 11.51 8.99997 11.51C11.3829 11.5099 13.6078 10.4125 15.167 8.53442C16.1104 7.39596 16.1109 5.62342 15.1679 4.4856C13.6025 2.60153 11.386 1.51008 8.99997 1.51001Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const NoEyeAiWorkflowIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 17 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.44907 1.05133L1.48883 1.08784L4.26311 3.86174C5.56999 3.15163 7.02551 2.77776 8.54688 2.77776C11.3168 2.77776 13.89 4.04472 15.6757 6.20535C17.004 7.79929 17.004 10.2603 15.6757 11.8542C15.1554 12.4786 14.5611 13.0353 13.9105 13.5094L16.4888 16.0878C16.7329 16.3319 16.7329 16.7276 16.4888 16.9717C16.2576 17.203 15.8903 17.2151 15.6447 17.0082L15.6049 16.9717L12.5433 13.911C12.5223 13.8948 12.5022 13.8771 12.4831 13.858L3.95215 5.31916L0.604947 1.97173C0.360869 1.72765 0.360869 1.33192 0.604946 1.08784C0.836178 0.856612 1.20352 0.844442 1.44907 1.05133ZM2.7497 5.68147C3.00028 5.91886 3.01098 6.31445 2.77358 6.56503L2.60174 6.74814L2.53646 6.8197L2.45383 6.91404L2.37838 7.00558C1.4362 8.13619 1.4362 9.91664 2.38001 11.056C3.93471 12.9371 6.15801 14.0318 8.54689 14.0318C9.20876 14.0318 9.83627 13.9552 10.4333 13.8005C10.7674 13.7138 11.1085 13.9145 11.1951 14.2486C11.2818 14.5827 11.0811 14.9238 10.747 15.0105C10.0452 15.1924 9.31242 15.2818 8.54689 15.2818C5.77696 15.2818 3.20374 14.0149 1.41694 11.8528C0.113291 10.2791 0.0904198 7.88483 1.34193 6.29919L1.47053 6.14095L1.56371 6.03215L1.67254 5.91177L1.86614 5.70535C2.10354 5.45477 2.49912 5.44408 2.7497 5.68147ZM8.54688 4.02776C7.41316 4.02776 6.32488 4.2677 5.32393 4.72767L5.19121 4.7901L6.26215 5.86103L6.32803 5.81447C6.95822 5.37875 7.72275 5.12353 8.54689 5.12353C10.7043 5.12353 12.4531 6.87242 12.4531 9.02978C12.4531 9.8829 12.1797 10.6721 11.7156 11.3146L13.0134 12.6123L13.0669 12.5762C13.6256 12.1823 14.1393 11.7186 14.5931 11.1975L14.7154 11.054C15.6574 9.92361 15.6574 8.13596 14.7138 7.00362C13.1591 5.12243 10.9358 4.02776 8.54688 4.02776ZM8.54689 6.41524C8.05113 6.41524 7.58772 6.55271 7.19268 6.79148L10.7862 10.3851L10.8208 10.3274C11.0402 9.9449 11.1656 9.50187 11.1656 9.02978C11.1656 7.58675 9.9941 6.41524 8.54689 6.41524Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const FolderIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12.8035 4.97778C12.8034 4.70167 12.5796 4.47778 12.3035 4.47778H8.58179L9.38892 5.47778L9.42676 5.51953C9.51975 5.61131 9.64569 5.66354 9.77783 5.66357H12.8035V4.97778ZM13.8035 5.77295C14.2146 5.9623 14.5 6.37782 14.5 6.86011V12C14.5 12.8284 13.8284 13.5 13 13.5H3C2.17157 13.5 1.5 12.8284 1.5 12V4C1.5 3.17157 2.17157 2.5 3 2.5H6.26855L6.35327 2.50244C6.77499 2.52629 7.16884 2.72714 7.43579 3.05786L7.77466 3.47778H12.3035C13.1319 3.47778 13.8034 4.14939 13.8035 4.97778V5.77295ZM2.5 12C2.5 12.2761 2.72386 12.5 3 12.5H13C13.2761 12.5 13.5 12.2761 13.5 12V6.86011C13.5 6.75162 13.412 6.66357 13.3035 6.66357H9.77783C9.35282 6.66354 8.94918 6.48337 8.66577 6.17017L8.61084 6.10571L6.65747 3.68604C6.57442 3.58314 6.45462 3.51787 6.32471 3.50317L6.26855 3.5H3C2.72386 3.5 2.5 3.72386 2.5 4V12Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const TargetIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.12319 4.41417C8.3688 3.80665 9.77476 3.60905 11.1396 3.84971C11.4795 3.90964 11.8037 3.68266 11.8636 3.34273C11.9235 3.0028 11.6966 2.67864 11.3566 2.6187C9.73185 2.33221 8.05809 2.56744 6.57523 3.29068C5.09237 4.01392 3.87653 5.18804 3.10198 6.64476C2.32743 8.10148 2.03393 9.76601 2.26354 11.3998C2.49315 13.0336 3.23409 14.5527 4.38016 15.7395C5.52624 16.9263 7.0186 17.7198 8.64337 18.0063C10.2681 18.2928 11.9419 18.0576 13.4248 17.3343C14.9076 16.6111 16.1235 15.437 16.898 13.9802C17.6726 12.5235 17.9661 10.859 17.7365 9.22521C17.6884 8.8834 17.3724 8.64524 17.0306 8.69328C16.6887 8.74132 16.4506 9.05736 16.4986 9.39918C16.6915 10.7716 16.445 12.1698 15.7943 13.3934C15.1437 14.6171 14.1224 15.6033 12.8768 16.2108C11.6312 16.8184 10.2252 17.0159 8.86043 16.7753C7.49563 16.5346 6.24204 15.8681 5.27934 14.8712C4.31664 13.8743 3.69425 12.5982 3.50137 11.2258C3.3085 9.85345 3.55504 8.45524 4.20566 7.2316C4.85629 6.00795 5.87759 5.02169 7.12319 4.41417Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.63009 7.50377C8.03695 7.79307 7.55061 8.26272 7.24079 8.8454C6.93097 9.42809 6.81357 10.0939 6.90542 10.7474C6.99726 11.4009 7.29364 12.0086 7.75207 12.4833C8.21049 12.958 8.80744 13.2754 9.45735 13.39C10.1073 13.5046 10.7768 13.4105 11.3699 13.1212C11.9631 12.8319 12.4494 12.3623 12.7592 11.7796C13.069 11.1969 13.1864 10.5311 13.0946 9.87759C13.0465 9.53577 13.2847 9.21973 13.6265 9.17169C13.9683 9.12365 14.2844 9.3618 14.3324 9.70362C14.461 10.6185 14.2966 11.5507 13.8629 12.3664C13.4291 13.1822 12.7483 13.8397 11.9179 14.2447C11.0875 14.6497 10.1502 14.7815 9.24029 14.621C8.33042 14.4606 7.49469 14.0162 6.85289 13.3516C6.21109 12.687 5.79616 11.8363 5.66758 10.9214C5.539 10.0065 5.70336 9.07433 6.13711 8.25856C6.57086 7.4428 7.25173 6.78529 8.08213 6.38028C8.91253 5.97527 9.84984 5.84354 10.7597 6.00397C11.0996 6.06391 11.3266 6.38807 11.2667 6.728C11.2067 7.06794 10.8826 7.29492 10.5427 7.23498C9.89274 7.12038 9.22324 7.21448 8.63009 7.50377Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.2682 2.90075C15.1384 2.44135 14.5623 2.29115 14.2248 2.62871L12.1962 4.65725C12.0486 4.80493 11.9844 5.01662 12.0253 5.22144L12.4008 7.10453C12.3975 7.10774 12.3941 7.111 12.3908 7.11431L10.3198 9.18537C9.90276 9.07401 9.43937 9.18191 9.11221 9.50907C8.62405 9.99723 8.62406 10.7887 9.11221 11.2768C9.60037 11.765 10.3918 11.765 10.88 11.2768C11.2071 10.9497 11.315 10.4863 11.2037 10.0692L13.2747 7.99819C13.278 7.99489 13.2813 7.99156 13.2845 7.9882L15.1676 8.36377C15.3724 8.40462 15.5841 8.34047 15.7318 8.19279L17.7603 6.16425C18.0979 5.82669 17.9477 5.25061 17.4883 5.12084L16.0937 4.7269C15.8846 4.66784 15.7212 4.50443 15.6621 4.29534L15.2682 2.90075ZM16.1228 6.03403L15.0843 7.07253L13.6104 6.77859L13.3165 5.30477L14.355 4.26626L14.4592 4.63514C14.6364 5.2624 15.1266 5.75264 15.7539 5.92983L16.1228 6.03403Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const SettingIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M11.2668 2.37273C11.4105 2.152 11.6796 2.04339 11.9391 2.10693L12.0819 2.14324C13.5509 2.5316 14.856 3.32023 15.8737 4.38476L15.9088 4.42443C16.0737 4.62885 16.0939 4.91747 15.9543 5.14434C15.6913 5.57157 15.6665 6.12364 15.9335 6.5863L15.986 6.67052C16.2429 7.05148 16.6599 7.27092 17.0959 7.2885L17.1896 7.28911L17.2427 7.28973C17.5051 7.30499 17.7327 7.48432 17.8067 7.74047C18.0138 8.45781 18.125 9.2157 18.125 9.99847L18.1238 10.1471C18.1104 10.8878 17.9971 11.605 17.7979 12.2855C17.7184 12.5567 17.4667 12.7407 17.1841 12.7344C16.7165 12.724 16.2595 12.9468 15.9854 13.3536L15.9332 13.4375C15.6687 13.8957 15.6908 14.4417 15.9473 14.8675L15.9726 14.9136C16.0882 15.1477 16.0463 15.4318 15.863 15.6229C14.8108 16.7194 13.4529 17.5217 11.925 17.8931C11.6516 17.9595 11.3673 17.8349 11.2308 17.5888C11.005 17.1818 10.5853 16.9001 10.0983 16.8664L10 16.8631C9.47126 16.8631 9.00967 17.1547 8.76892 17.5888C8.63242 17.835 8.34816 17.9595 8.07465 17.8931C6.54689 17.5217 5.18889 16.7195 4.13666 15.6229C3.94122 15.4191 3.90675 15.1094 4.05243 14.8675C4.29284 14.4682 4.32739 13.9635 4.11285 13.5245L4.06647 13.4375C3.81744 13.0063 3.3743 12.756 2.90894 12.7356L2.81586 12.7344C2.53329 12.7407 2.28165 12.5567 2.20215 12.2855C2.00291 11.605 1.88959 10.8878 1.87622 10.1471L1.875 9.99847C1.87501 9.2159 1.98583 8.45796 2.19299 7.74047L2.20978 7.69012C2.30375 7.44462 2.54348 7.2821 2.81006 7.28911C3.31079 7.30229 3.79989 7.04797 4.06647 6.5863L4.11316 6.49871C4.32954 6.05533 4.29213 5.54505 4.04541 5.14434C3.89645 4.90233 3.92961 4.59019 4.12598 4.38476L4.22516 4.28252C5.26127 3.23585 6.58122 2.46937 8.06061 2.10693L8.11249 2.09655C8.37265 2.05566 8.63331 2.18288 8.75977 2.41821L8.80707 2.49969C9.0564 2.89781 9.49795 3.1607 10 3.1607L10.0995 3.15734C10.5927 3.12287 11.0167 2.83387 11.2399 2.41821L11.2668 2.37273ZM12.0544 3.43688C11.5865 4.00767 10.8852 4.38148 10.0946 4.40917L10 4.4107C9.17087 4.4107 8.43123 4.03062 7.94464 3.43688C6.9619 3.74429 6.07355 4.26622 5.33478 4.94903C5.59354 5.63871 5.56555 6.43148 5.19501 7.12859L5.14923 7.2113C4.73655 7.92605 4.04192 8.3753 3.28857 8.50219C3.18179 8.98342 3.12501 9.4841 3.125 9.99847L3.12958 10.2496C3.14529 10.6858 3.2021 11.1112 3.29529 11.5225C4.04605 11.6509 4.73779 12.0999 5.14923 12.8125L5.1944 12.8943C5.56181 13.5848 5.59273 14.369 5.3421 15.054C6.084 15.7378 6.97619 16.2593 7.96295 16.5649C8.44967 15.9839 9.18122 15.6131 10 15.6131L10.0934 15.6146C10.8742 15.6416 11.5684 16.0063 12.0364 16.5649C13.0232 16.2593 13.9153 15.7379 14.6573 15.054C14.3967 14.3418 14.4407 13.5222 14.8505 12.8125L14.899 12.7319C15.315 12.0661 15.9824 11.6461 16.7044 11.5225C16.8154 11.0326 16.875 10.5227 16.875 9.99847C16.875 9.48402 16.8175 8.98334 16.7105 8.50188C15.9863 8.37965 15.3164 7.95976 14.8993 7.29217L14.8508 7.2113C14.4372 6.49487 14.3957 5.66619 14.6646 4.94903C13.9257 4.26614 13.0373 3.74422 12.0544 3.43688ZM10 6.56249C11.8985 6.56249 13.4375 8.10151 13.4375 9.99999C13.4375 11.8985 11.8985 13.4375 10 13.4375C8.10153 13.4375 6.56251 11.8985 6.5625 9.99999C6.5625 8.10151 8.10152 6.56249 10 6.56249ZM10 7.81249C8.79188 7.81249 7.8125 8.79187 7.8125 9.99999C7.81251 11.2081 8.79189 12.1875 10 12.1875C11.2081 12.1875 12.1875 11.2081 12.1875 9.99999C12.1875 8.79187 11.2081 7.81249 10 7.81249Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const CloseRedIcon = (props: SvgIconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="currentColor"
      {...props}
    >
      <path
        d="M10 1.875C14.4873 1.875 18.125 5.51269 18.125 10C18.125 14.4873 14.4873 18.125 10 18.125C5.51269 18.125 1.875 14.4873 1.875 10C1.875 5.51269 5.51269 1.875 10 1.875ZM13.418 6.71875C13.1739 6.47467 12.7783 6.47467 12.5342 6.71875L9.99902 9.25293L7.46484 6.71875C7.22077 6.47467 6.82513 6.47467 6.58105 6.71875C6.33737 6.96286 6.33711 7.35859 6.58105 7.60254L9.11523 10.1367L6.58105 12.6709C6.3371 12.9149 6.33735 13.3106 6.58105 13.5547C6.82513 13.7988 7.22077 13.7988 7.46484 13.5547L9.99902 11.0195L12.5342 13.5547C12.7783 13.7988 13.1739 13.7988 13.418 13.5547C13.662 13.3106 13.662 12.9149 13.418 12.6709L10.8828 10.1367L13.418 7.60254C13.662 7.3585 13.6619 6.96284 13.418 6.71875Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const UserInfoIcon = (props: SvgIconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 20 21"
      fill="currentColor"
      {...props}
    >
      <path
        d="M15.1418 3C16.9361 3.00007 18.3331 4.72668 18.3333 6.79688V14.2031C18.3331 16.2733 16.9361 17.9999 15.1418 18H4.86646C3.07222 17.9998 1.67523 16.2733 1.67505 14.2031V6.79688C1.67523 4.72674 3.07222 3.00017 4.86646 3H15.1418ZM4.86646 4.25C3.82778 4.25015 2.92522 5.36571 2.92505 6.79688V14.2031C2.92522 15.6343 3.82778 16.7499 4.86646 16.75H15.1418C16.1805 16.7499 17.0831 15.6343 17.0833 14.2031V6.79688C17.0831 5.36569 16.1805 4.25012 15.1418 4.25H4.86646ZM14.3284 11.125C15.5707 11.125 16.5627 12.171 16.5627 13.4434V14.25C16.5627 14.5951 16.2828 14.8749 15.9377 14.875C15.5926 14.875 15.3127 14.5952 15.3127 14.25V13.4434C15.3127 12.8468 14.8649 12.375 14.3284 12.375H11.9211C11.3856 12.375 10.9377 12.8457 10.9377 13.4434V14.25C10.9377 14.5951 10.6578 14.8749 10.3127 14.875C9.96757 14.875 9.68774 14.5952 9.68774 14.25V13.4434C9.68775 12.1686 10.6812 11.125 11.9211 11.125H14.3284ZM8.28345 12.375C8.62857 12.3751 8.90845 12.6549 8.90845 13C8.90845 13.3451 8.62857 13.6249 8.28345 13.625H4.85864C4.51346 13.625 4.23364 13.3452 4.23364 13C4.23364 12.6548 4.51346 12.375 4.85864 12.375H8.28345ZM8.28345 9.80859C8.62857 9.80866 8.90845 10.0885 8.90845 10.4336C8.9083 10.7786 8.62848 11.0585 8.28345 11.0586H4.85864C4.51356 11.0586 4.23379 10.7786 4.23364 10.4336C4.23364 10.0884 4.51346 9.80859 4.85864 9.80859H8.28345ZM13.1252 6.4375C14.3333 6.43763 15.3127 7.41696 15.3127 8.625C15.3127 9.83304 14.3333 10.8124 13.1252 10.8125C11.9171 10.8125 10.9377 9.83313 10.9377 8.625C10.9377 7.41688 11.9171 6.4375 13.1252 6.4375ZM13.1252 7.6875C12.6075 7.6875 12.1877 8.10723 12.1877 8.625C12.1877 9.14277 12.6075 9.5625 13.1252 9.5625C13.6429 9.56237 14.0627 9.14269 14.0627 8.625C14.0627 8.10732 13.6429 7.68763 13.1252 7.6875ZM5.71704 7.2334C6.06204 7.23361 6.34204 7.51335 6.34204 7.8584C6.342 8.20341 6.06202 8.48319 5.71704 8.4834H4.85864C4.51349 8.4834 4.23368 8.20354 4.23364 7.8584C4.23364 7.51322 4.51346 7.2334 4.85864 7.2334H5.71704Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const TeamInfoIcon = (props: SvgIconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 20 21"
      fill="currentColor"
      {...props}
    >
      <path
        d="M10.1562 3.3125C12.2273 3.3125 13.9062 4.99143 13.9062 7.0625C13.9062 8.51422 13.0735 9.81645 11.7904 10.4385C11.4798 10.5891 11.1059 10.4594 10.9553 10.1488C10.8048 9.83815 10.9345 9.46429 11.2451 9.31372C12.1012 8.89868 12.6562 8.0307 12.6562 7.0625C12.6562 5.68179 11.537 4.5625 10.1562 4.5625C8.77554 4.5625 7.65625 5.68179 7.65625 7.0625C7.65625 8.13244 8.33461 9.07341 9.32769 9.42211C9.65337 9.53646 9.82469 9.89319 9.71033 10.2189C9.59597 10.5446 9.23925 10.7159 8.91356 10.6015C7.42351 10.0783 6.40625 8.66725 6.40625 7.0625C6.40625 4.99143 8.08518 3.3125 10.1562 3.3125Z"
        fill="currentColor"
      />
      <path
        d="M5.67907 4.68282C6.01155 4.59005 6.35628 4.78438 6.44905 5.11686C6.54182 5.44933 6.34749 5.79406 6.01501 5.88683C5.14485 6.12962 4.53125 6.92628 4.53125 7.84361C4.53125 8.62326 4.97409 9.3232 5.66021 9.66412C5.96933 9.81771 6.09541 10.1928 5.94182 10.5019C5.78823 10.8111 5.41312 10.9371 5.104 10.7835C3.99682 10.2334 3.28125 9.10242 3.28125 7.84361C3.28125 6.36171 4.27197 5.07543 5.67907 4.68282Z"
        fill="currentColor"
      />
      <path
        d="M13.9816 5.12414C14.0889 4.79604 14.4418 4.61698 14.7699 4.7242C16.1082 5.16156 17.0312 6.41368 17.0312 7.84366C17.0312 9.03419 16.3913 10.1136 15.3768 10.6937C15.0772 10.8651 14.6954 10.761 14.524 10.4614C14.3527 10.1618 14.4567 9.77994 14.7563 9.60859C15.3853 9.24891 15.7812 8.58117 15.7812 7.84366C15.7812 6.95838 15.2096 6.18295 14.3816 5.91237C14.0535 5.80515 13.8744 5.45225 13.9816 5.12414Z"
        fill="currentColor"
      />
      <path
        d="M5.18624 9.62028C5.52358 9.54714 5.85633 9.76132 5.92947 10.0987C6.00261 10.436 5.78843 10.7688 5.45109 10.8419C3.74087 11.2127 2.5 12.7334 2.5 14.5067C2.5 14.7927 2.53189 15.074 2.59443 15.3473C2.67143 15.6838 2.46107 16.019 2.12459 16.096C1.78811 16.173 1.45292 15.9626 1.37592 15.6261C1.29249 15.2615 1.25 14.8867 1.25 14.5067C1.25 12.1421 2.90398 10.1151 5.18624 9.62028Z"
        fill="currentColor"
      />
      <path
        d="M14.3924 10.0409C14.4734 9.70532 14.811 9.4989 15.1465 9.57982C17.3744 10.1171 18.9707 12.1181 18.9707 14.4406C18.9707 14.8264 18.9269 15.207 18.8409 15.5768C18.7627 15.9131 18.4268 16.1222 18.0906 16.0441C17.7544 15.9659 17.5452 15.63 17.6234 15.2938C17.6878 15.0165 17.7207 14.731 17.7207 14.4406C17.7207 12.6989 16.5231 11.1976 14.8535 10.795C14.5179 10.7141 14.3115 10.3764 14.3924 10.0409Z"
        fill="currentColor"
      />
      <path
        d="M9.62234 9.86536C9.69043 10.2038 9.47131 10.5333 9.13291 10.6014C6.74516 11.0818 5 13.1897 5 15.6562C5 15.8154 5.00719 15.9735 5.02147 16.1304L5.03469 16.2568L15.2779 16.2569C15.3009 16.0587 15.3125 15.8583 15.3125 15.6562C15.3125 13.2105 13.5963 11.1152 11.235 10.6128C10.8974 10.541 10.6819 10.2091 10.7538 9.87147C10.8256 9.53385 11.1575 9.31838 11.4951 9.3902C14.4307 10.0147 16.5625 12.6174 16.5625 15.6562C16.5625 16.1188 16.5134 16.5754 16.4168 17.0205C16.3436 17.3579 16.0108 17.572 15.6734 17.4988L15.6369 17.4897C15.6047 17.4974 15.5716 17.5026 15.5377 17.5051L15.491 17.5069H4.0007L3.89425 17.0137C3.79863 16.5708 3.75 16.1164 3.75 15.6562C3.75 12.5916 5.91773 9.97325 8.88634 9.37593C9.22473 9.30784 9.55426 9.52696 9.62234 9.86536Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const LogoutIcon = (props: SvgIconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="currentColor"
      {...props}
    >
      <path
        d="M12.6679 2.23291C13.6512 2.23291 14.4534 3.0115 14.4902 3.98584L14.4911 4.05615V6.15479C14.491 6.49983 14.2112 6.77979 13.8661 6.77979C13.5367 6.77979 13.267 6.52408 13.2431 6.20068L13.2411 6.15479V4.05615C13.2411 3.75556 13.0092 3.50876 12.7148 3.48486L12.6679 3.48291H4.28314C3.98255 3.48291 3.73575 3.71484 3.71185 4.00928L3.7099 4.05615V16.0347C3.7099 16.3353 3.94183 16.5821 4.23627 16.606L4.28314 16.6079H12.6679C12.9685 16.6079 13.2153 16.376 13.2392 16.0815L13.2411 16.0347V13.8657C13.2411 13.5205 13.521 13.2407 13.8661 13.2407C14.1956 13.2407 14.4653 13.4955 14.4892 13.8188L14.4911 13.8657V16.0347C14.4911 17.018 13.7126 17.8202 12.7382 17.8569L12.6679 17.8579H4.28314C3.2998 17.8579 2.49765 17.0793 2.46088 16.105L2.4599 16.0347V4.05615C2.4599 3.07281 3.23849 2.27066 4.21283 2.23389L4.28314 2.23291H12.6679ZM14.3779 7.09717C14.6083 6.86536 14.9755 6.85212 15.2216 7.05811L15.2617 7.09424L17.7392 9.55615C17.7543 9.57116 17.766 9.58888 17.7792 9.60498C17.8183 9.65248 17.851 9.70283 17.874 9.75732C17.8806 9.77316 17.8843 9.78993 17.8896 9.80615C17.8967 9.82792 17.9045 9.8493 17.9091 9.87158C17.9117 9.88415 17.9122 9.89699 17.914 9.90967C17.9179 9.93634 17.9214 9.96285 17.9218 9.98975C17.922 10.0018 17.9204 10.0138 17.9199 10.0259C17.9187 10.0527 17.9176 10.0794 17.913 10.106C17.9107 10.1195 17.9065 10.1326 17.9033 10.146C17.8972 10.1712 17.89 10.1958 17.8808 10.2202C17.8754 10.2345 17.8687 10.2482 17.8622 10.2622C17.8528 10.2825 17.8427 10.3024 17.831 10.3218C17.8212 10.338 17.8102 10.3532 17.7988 10.3687C17.7905 10.3798 17.7844 10.3931 17.7753 10.4038L17.7392 10.4429L15.2617 12.9048C15.0169 13.148 14.6212 13.1474 14.3779 12.9028C14.1474 12.6709 14.1361 12.303 14.3437 12.0581L14.3808 12.0181L15.7822 10.6245H9.76849C9.42343 10.6244 9.14349 10.3446 9.14349 9.99951C9.14366 9.67033 9.39856 9.40055 9.72162 9.37646L9.76849 9.37451H15.7822L14.3808 7.98096C14.136 7.73763 14.1345 7.34199 14.3779 7.09717Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const ModelIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_3498_15106)">
        <path
          d="M16.0001 46.9333L10.6667 44V37.3333"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.6667 26.6666V20L16.0001 17.0667"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M26.6667 10.9333L32.0001 8L37.3334 10.9333"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M48 17.0667L53.3333 20V26.6666"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M53.3333 37.3333V44L48 46.9867"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M37.3334 53.0667L32.0001 56L26.6667 53.0667"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M32 32L37.3333 29.0667"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M48 22.9333L53.3333 20"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M32 32V38.6667"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M32 49.3333V56"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M32.0001 32L26.6667 29.0134"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M16.0001 22.9333L10.6667 20"
          stroke="currentColor"
          strokeWidth="4"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_3498_15106">
          <rect width="64" height="64" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const AppIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.63965 16.1738C6.79947 15.868 7.17654 15.7495 7.48242 15.9092C8.30036 16.3366 9.21169 16.5625 10.1562 16.5625C10.971 16.5625 11.761 16.3939 12.4893 16.0723C12.8049 15.9328 13.1739 16.0761 13.3135 16.3916C13.4528 16.7072 13.3105 17.0762 12.9951 17.2158C12.1085 17.6074 11.1459 17.8125 10.1562 17.8125C9.00887 17.8125 7.8989 17.5369 6.90332 17.0166C6.59795 16.8566 6.48021 16.4795 6.63965 16.1738ZM4.375 10.9375C5.9283 10.9375 7.1875 12.1967 7.1875 13.75C7.1875 15.3033 5.9283 16.5625 4.375 16.5625C2.8217 16.5625 1.5625 15.3033 1.5625 13.75C1.5625 12.1967 2.8217 10.9375 4.375 10.9375ZM15.625 10.9375C17.1783 10.9375 18.4375 12.1967 18.4375 13.75C18.4375 15.3033 17.1783 16.5625 15.625 16.5625C14.0717 16.5625 12.8125 15.3033 12.8125 13.75C12.8125 12.1967 14.0717 10.9375 15.625 10.9375ZM4.375 12.1875C3.51206 12.1875 2.8125 12.8871 2.8125 13.75C2.8125 14.6129 3.51206 15.3125 4.375 15.3125C5.23794 15.3125 5.9375 14.6129 5.9375 13.75C5.9375 12.8871 5.23794 12.1875 4.375 12.1875ZM15.625 12.1875C14.7621 12.1875 14.0625 12.8871 14.0625 13.75C14.0625 14.6129 14.7621 15.3125 15.625 15.3125C16.4879 15.3125 17.1875 14.6129 17.1875 13.75C17.1875 12.8871 16.4879 12.1875 15.625 12.1875ZM13.5869 5.33496C13.7985 5.06235 14.1912 5.01218 14.4639 5.22363C15.8951 6.33448 16.8525 7.95765 17.1143 9.7666C17.1635 10.108 16.9272 10.425 16.5859 10.4746C16.2445 10.5239 15.9265 10.2867 15.877 9.94531C15.6619 8.45944 14.8748 7.1259 13.6973 6.21191C13.4248 6.0004 13.3757 5.60762 13.5869 5.33496ZM5.60352 5.42285C5.86643 5.19951 6.26082 5.23138 6.48438 5.49414C6.70789 5.75703 6.67585 6.15137 6.41309 6.375C5.38122 7.25262 4.68357 8.46501 4.45605 9.80859C4.39831 10.1488 4.07559 10.3789 3.73535 10.3213C3.39538 10.2635 3.16633 9.94063 3.22363 9.60059C3.50052 7.96465 4.34931 6.48955 5.60352 5.42285ZM10 1.875C11.5533 1.875 12.8125 3.1342 12.8125 4.6875C12.8125 6.2408 11.5533 7.5 10 7.5C8.4467 7.5 7.1875 6.2408 7.1875 4.6875C7.1875 3.1342 8.4467 1.875 10 1.875ZM10 3.125C9.13705 3.125 8.4375 3.82456 8.4375 4.6875C8.4375 5.55044 9.13705 6.25 10 6.25C10.8629 6.25 11.5625 5.55044 11.5625 4.6875C11.5625 3.82456 10.8629 3.125 10 3.125Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const EllipsisIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.75 8C11.75 8.69036 12.3096 9.25 13 9.25C13.6904 9.25 14.25 8.69036 14.25 8C14.25 7.30964 13.6904 6.75 13 6.75C12.3096 6.75 11.75 7.30964 11.75 8ZM6.75 8C6.75 8.69036 7.30964 9.25 8 9.25C8.69036 9.25 9.25 8.69036 9.25 8C9.25 7.30964 8.69036 6.75 8 6.75C7.30964 6.75 6.75 7.30964 6.75 8ZM1.75 8C1.75 8.69036 2.30964 9.25 3 9.25C3.69036 9.25 4.25 8.69036 4.25 8C4.25 7.30964 3.69036 6.75 3 6.75C2.30964 6.75 1.75 7.30964 1.75 8Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const TriangleIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M3.5 3.66727C3.50007 2.66903 4.57035 2.03625 5.44504 2.51733L11.5045 5.85007C12.4109 6.34875 12.4109 7.65125 11.5045 8.14993L5.44504 11.4827C4.57035 11.9637 3.50007 11.331 3.5 10.3327L3.5 3.66727ZM4.375 10.3327C4.37507 10.6654 4.7318 10.8763 5.02335 10.716L11.0828 7.38324C11.3848 7.217 11.3848 6.783 11.0828 6.61676L5.02335 3.28403C4.7318 3.12368 4.37507 3.33456 4.375 3.66727L4.375 10.3327Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const ExpandIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M13.75 12.375C14.0261 12.375 14.25 12.5989 14.25 12.875C14.25 13.1511 14.0261 13.375 13.75 13.375H2.25C1.97386 13.375 1.75 13.1511 1.75 12.875C1.75 12.5989 1.97386 12.375 2.25 12.375H13.75ZM12.2249 9.18555C12.4201 8.99036 12.7366 8.99041 12.9319 9.18555C13.1271 9.38081 13.1271 9.69756 12.9319 9.89282L11.5178 11.3069C11.3226 11.5021 11.0061 11.5021 10.8108 11.3069L9.39648 9.89282C9.20122 9.69756 9.20122 9.38081 9.39648 9.18555C9.59172 8.99038 9.90827 8.99039 10.1035 9.18555L11.1643 10.2463L12.2249 9.18555ZM7.02661 9.12769C7.27872 9.15331 7.47559 9.36613 7.47559 9.625C7.47559 9.88387 7.27872 10.0967 7.02661 10.1223L6.97559 10.125H3C2.72386 10.125 2.5 9.90114 2.5 9.625C2.5 9.34886 2.72386 9.125 3 9.125H6.97559L7.02661 9.12769ZM10.8105 5.02148C11.0058 4.82622 11.3226 4.82622 11.5178 5.02148L12.9319 6.43555C13.1271 6.63081 13.1271 6.94756 12.9319 7.14282C12.7366 7.33801 12.4201 7.33804 12.2249 7.14282L11.1643 6.08203L10.1035 7.14282C9.90827 7.33803 9.59174 7.33802 9.39648 7.14282C9.20122 6.94756 9.20122 6.63081 9.39648 6.43555L10.8105 5.02148ZM7.02661 5.87769C7.27872 5.90331 7.47559 6.11613 7.47559 6.375C7.47559 6.63387 7.27872 6.84669 7.02661 6.87231L6.97559 6.875H3C2.72386 6.875 2.5 6.65114 2.5 6.375C2.5 6.09886 2.72386 5.875 3 5.875H6.97559L7.02661 5.87769ZM13.75 2.625C14.0261 2.625 14.25 2.84886 14.25 3.125C14.25 3.40114 14.0261 3.625 13.75 3.625H2.25C1.97386 3.625 1.75 3.40114 1.75 3.125C1.75 2.84886 1.97386 2.625 2.25 2.625H13.75Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const CollapseIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M13.75 12.375C14.0261 12.375 14.25 12.5989 14.25 12.875C14.25 13.1511 14.0261 13.375 13.75 13.375H2.25C1.97386 13.375 1.75 13.1511 1.75 12.875C1.75 12.5989 1.97386 12.375 2.25 12.375H13.75ZM10.6912 9.10718C10.8864 8.91199 11.203 8.91196 11.3982 9.10718L12.8125 10.5215C13.0077 10.7167 13.0077 11.0333 12.8125 11.2285C12.6172 11.4238 12.3005 11.4238 12.1052 11.2285L11.0447 10.168L9.98389 11.2285C9.78862 11.4238 9.47211 11.4238 9.27686 11.2285C9.08163 11.0333 9.08163 10.7167 9.27686 10.5215L10.6912 9.10718ZM7.02661 9.12769C7.27872 9.15331 7.47559 9.36613 7.47559 9.625C7.47559 9.88387 7.27872 10.0967 7.02661 10.1223L6.97559 10.125H3C2.72386 10.125 2.5 9.90114 2.5 9.625C2.5 9.34886 2.72386 9.125 3 9.125H6.97559L7.02661 9.12769ZM12.1052 4.77148C12.3005 4.57623 12.617 4.57626 12.8123 4.77148C13.0075 4.96675 13.0075 5.28325 12.8123 5.47852L11.3982 6.89282C11.203 7.08805 10.8864 7.088 10.6912 6.89282L9.27686 5.47852C9.0816 5.28326 9.0816 4.96674 9.27686 4.77148C9.47212 4.57622 9.78862 4.57623 9.98389 4.77148L11.0447 5.83203L12.1052 4.77148ZM7.02661 5.87769C7.27872 5.90331 7.47559 6.11613 7.47559 6.375C7.47559 6.63387 7.27872 6.84669 7.02661 6.87231L6.97559 6.875H3C2.72386 6.875 2.5 6.65114 2.5 6.375C2.5 6.09886 2.72386 5.875 3 5.875H6.97559L7.02661 5.87769ZM13.75 2.625C14.0261 2.625 14.25 2.84886 14.25 3.125C14.25 3.40114 14.0261 3.625 13.75 3.625H2.25C1.97386 3.625 1.75 3.40114 1.75 3.125C1.75 2.84886 1.97386 2.625 2.25 2.625H13.75Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const ShiningIcon = (props: SvgIconProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.80139 4.01428C6.91344 4.08174 7.02979 4.20793 7.0606 4.39456C7.12661 4.79444 7.24827 5.39735 7.45065 5.99485C7.65697 6.60401 7.92845 7.14998 8.26421 7.48575C8.59998 7.82151 9.14595 8.09299 9.75511 8.29931C10.3526 8.50169 10.9555 8.62335 11.3554 8.68936C11.542 8.72017 11.6682 8.83652 11.7357 8.94856C11.7986 9.05301 11.8184 9.16175 11.8184 9.24998C11.8184 9.3382 11.7986 9.44694 11.7357 9.55139C11.6682 9.66344 11.542 9.77979 11.3554 9.8106C10.9555 9.87661 10.3526 9.99827 9.75511 10.2006C9.14595 10.407 8.59998 10.6784 8.26421 11.0142C7.92845 11.35 7.65697 11.896 7.45065 12.5051C7.24827 13.1026 7.12661 13.7055 7.0606 14.1054C7.02979 14.292 6.91344 14.4182 6.80139 14.4857C6.69694 14.5486 6.58821 14.5684 6.49998 14.5684C6.41176 14.5684 6.30302 14.5486 6.19857 14.4857C6.08652 14.4182 5.97017 14.292 5.93936 14.1054C5.87335 13.7055 5.75169 13.1026 5.54931 12.5051C5.34299 11.896 5.07151 11.35 4.73575 11.0142C4.39998 10.6784 3.85401 10.407 3.24485 10.2006C2.64735 9.99827 2.04444 9.87661 1.64456 9.8106C1.45793 9.77979 1.33174 9.66344 1.26428 9.55139C1.2014 9.44694 1.18152 9.33821 1.18152 9.24998C1.18152 9.16176 1.2014 9.05302 1.26428 8.94857C1.33174 8.83652 1.45793 8.72017 1.64456 8.68936C2.04444 8.62335 2.64735 8.50169 3.24485 8.29931C3.85401 8.09299 4.39998 7.82151 4.73575 7.48575C5.07151 7.14998 5.34299 6.60401 5.54931 5.99485C5.75169 5.39735 5.87335 4.79444 5.93936 4.39456C5.97017 4.20793 6.08652 4.08174 6.19856 4.01428C6.30301 3.9514 6.41175 3.93152 6.49998 3.93152C6.5882 3.93152 6.69694 3.9514 6.80139 4.01428ZM6.49998 6.30524C6.49881 6.30871 6.49763 6.31218 6.49646 6.31566C6.2741 6.97215 5.94229 7.69342 5.44285 8.19285C4.94342 8.69229 4.22215 9.0241 3.56566 9.24646C3.56218 9.24763 3.55871 9.24881 3.55524 9.24998C3.55871 9.25115 3.56218 9.25233 3.56566 9.2535C4.22215 9.47586 4.94342 9.80767 5.44285 10.3071C5.94229 10.8065 6.2741 11.5278 6.49646 12.1843C6.49763 12.1878 6.49881 12.1912 6.49998 12.1947C6.50115 12.1912 6.50233 12.1878 6.5035 12.1843C6.72586 11.5278 7.05767 10.8065 7.5571 10.3071C8.05654 9.80767 8.77781 9.47586 9.4343 9.2535C9.43778 9.25233 9.44125 9.25115 9.44472 9.24998C9.44125 9.24881 9.43778 9.24763 9.4343 9.24646C8.77781 9.0241 8.05654 8.69229 7.5571 8.19285C7.05767 7.69342 6.72586 6.97215 6.5035 6.31566C6.50233 6.31218 6.50115 6.30871 6.49998 6.30524Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.1546 1.04302C12.2605 1.10678 12.3724 1.2274 12.402 1.40662C12.4338 1.59929 12.4919 1.8862 12.5871 2.16729C12.6863 2.46003 12.8069 2.68919 12.9339 2.81616C13.0608 2.94312 13.29 3.06378 13.5828 3.16293C13.8638 3.25814 14.1507 3.31627 14.3434 3.34808C14.5226 3.37766 14.6433 3.4895 14.707 3.59539C14.7662 3.69369 14.7842 3.79462 14.7842 3.87505C14.7842 3.95547 14.7662 4.05641 14.707 4.1547C14.6433 4.2606 14.5226 4.37244 14.3434 4.40202C14.1507 4.43383 13.8638 4.49196 13.5828 4.58717C13.29 4.68632 13.0608 4.80698 12.9339 4.93394C12.8069 5.06091 12.6863 5.29007 12.5871 5.58282C12.4919 5.8639 12.4338 6.15081 12.402 6.34348C12.3724 6.5227 12.2605 6.64332 12.1546 6.70708C12.0564 6.76626 11.9554 6.78428 11.875 6.78428C11.7946 6.78428 11.6936 6.76626 11.5953 6.70708C11.4894 6.64333 11.3776 6.5227 11.348 6.34348C11.3162 6.15081 11.2581 5.8639 11.1629 5.58282C11.0637 5.29007 10.9431 5.06091 10.8161 4.93394C10.6891 4.80698 10.46 4.68633 10.1672 4.58717C9.88614 4.49196 9.59923 4.43383 9.40656 4.40202C9.22734 4.37244 9.10672 4.2606 9.04296 4.15471C8.98378 4.05641 8.96576 3.95548 8.96576 3.87505C8.96576 3.79463 8.98378 3.69369 9.04296 3.5954C9.10672 3.48951 9.22734 3.37766 9.40656 3.34808C9.59923 3.31627 9.88614 3.25814 10.1672 3.16293C10.46 3.06378 10.6891 2.94312 10.8161 2.81616C10.9431 2.68919 11.0637 2.46003 11.1629 2.16729C11.2581 1.8862 11.3162 1.59929 11.348 1.40662C11.3776 1.2274 11.4894 1.10678 11.5953 1.04303C11.6936 0.983846 11.7946 0.965821 11.875 0.96582C11.9554 0.96582 12.0563 0.983844 12.1546 1.04302ZM11.875 3.03802C11.7804 3.21305 11.6648 3.38167 11.5232 3.52327C11.3816 3.66486 11.213 3.78044 11.038 3.87505C11.213 3.96967 11.3816 4.08524 11.5232 4.22684C11.6648 4.36844 11.7804 4.53705 11.875 4.71208C11.9696 4.53705 12.0852 4.36844 12.2268 4.22684C12.3684 4.08524 12.537 3.96967 12.712 3.87505C12.537 3.78044 12.3684 3.66486 12.2268 3.52327C12.0852 3.38167 11.9696 3.21305 11.875 3.03802Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const ControlSquareIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.5625 1.93265C17.598 1.93265 18.4375 2.77211 18.4375 3.80765V16.9326C18.4375 17.9682 17.598 18.8076 16.5625 18.8076H3.4375C2.40197 18.8076 1.5625 17.9682 1.5625 16.9326V3.80765C1.5625 2.77211 2.40197 1.93265 3.4375 1.93265H16.5625ZM3.4375 3.18265C3.09232 3.18265 2.8125 3.46247 2.8125 3.80765V16.9326C2.8125 17.2778 3.09232 17.5576 3.4375 17.5576H16.5625C16.9077 17.5576 17.1875 17.2778 17.1875 16.9326V3.80765C17.1875 3.46247 16.9077 3.18265 16.5625 3.18265H3.4375ZM5.78125 4.2764C6.12643 4.2764 6.40625 4.55622 6.40625 4.9014V6.09378C7.22217 6.35745 7.8125 7.12271 7.8125 8.0264C7.8125 8.92998 7.22202 9.69428 6.40625 9.95804V15.8389C6.40625 16.1841 6.12643 16.4639 5.78125 16.4639C5.43607 16.4639 5.15625 16.1841 5.15625 15.8389V9.95804C4.34048 9.69428 3.75 8.92998 3.75 8.0264C3.75 7.12271 4.34033 6.35745 5.15625 6.09378V4.9014C5.15625 4.55622 5.43607 4.2764 5.78125 4.2764ZM9.84375 4.2764C10.1889 4.2764 10.4688 4.55622 10.4688 4.9014V10.7813C11.2847 11.045 11.875 11.8102 11.875 12.7139C11.875 13.6175 11.2845 14.3818 10.4688 14.6455V15.8389C10.4688 16.1841 10.1889 16.4639 9.84375 16.4639C9.49857 16.4639 9.21875 16.1841 9.21875 15.8389V14.6455C8.40298 14.3818 7.8125 13.6175 7.8125 12.7139C7.8125 11.8102 8.40283 11.045 9.21875 10.7813V4.9014C9.21875 4.55622 9.49857 4.2764 9.84375 4.2764ZM13.9062 4.2764C14.2514 4.2764 14.5312 4.55622 14.5312 4.9014V6.09378C15.3472 6.35745 15.9375 7.12271 15.9375 8.0264C15.9375 8.92998 15.347 9.69428 14.5312 9.95804V15.8389C14.5312 16.1841 14.2514 16.4639 13.9062 16.4639C13.5611 16.4639 13.2812 16.1841 13.2812 15.8389V9.95804C12.4655 9.69428 11.875 8.92998 11.875 8.0264C11.875 7.12271 12.4653 6.35745 13.2812 6.09378V4.9014C13.2812 4.55622 13.5611 4.2764 13.9062 4.2764ZM9.84375 11.9326C9.41228 11.9326 9.0625 12.2824 9.0625 12.7139C9.0625 13.1454 9.41228 13.4951 9.84375 13.4951C10.2752 13.4951 10.625 13.1454 10.625 12.7139C10.625 12.2824 10.2752 11.9326 9.84375 11.9326ZM5.78125 7.24515C5.34978 7.24515 5 7.59493 5 8.0264C5 8.45787 5.34978 8.80765 5.78125 8.80765C6.21272 8.80765 6.5625 8.45787 6.5625 8.0264C6.5625 7.59493 6.21272 7.24515 5.78125 7.24515ZM13.9062 7.24515C13.4748 7.24515 13.125 7.59493 13.125 8.0264C13.125 8.45787 13.4748 8.80765 13.9062 8.80765C14.3377 8.80765 14.6875 8.45787 14.6875 8.0264C14.6875 7.59493 14.3377 7.24515 13.9062 7.24515Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const GlobalIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.875 10.3701C16.875 6.57319 13.797 3.49515 10 3.49515C6.20304 3.49515 3.125 6.57319 3.125 10.3701C3.125 14.1671 6.20304 17.2451 10 17.2451C13.797 17.2451 16.875 14.1671 16.875 10.3701ZM18.125 10.3701C18.125 14.8575 14.4873 18.4951 10 18.4951C5.51269 18.4951 1.875 14.8575 1.875 10.3701C1.875 5.88283 5.51269 2.24515 10 2.24515C14.4873 2.24515 18.125 5.88283 18.125 10.3701Z"
        fill="currentColor"
      />
      <path
        d="M16.9999 7.24515C17.3451 7.24515 17.6249 7.52497 17.6249 7.87015C17.6249 8.21533 17.3451 8.49515 16.9999 8.49515H3C2.65482 8.49515 2.375 8.21533 2.375 7.87015C2.375 7.52497 2.65482 7.24515 3 7.24515H16.9999Z"
        fill="currentColor"
      />
      <path
        d="M16.9999 12.2451C17.3451 12.2451 17.6249 12.525 17.6249 12.8701C17.6249 13.2153 17.3451 13.4951 16.9999 13.4951H3C2.65482 13.4951 2.375 13.2153 2.375 12.8701C2.375 12.525 2.65482 12.2451 3 12.2451H16.9999Z"
        fill="currentColor"
      />
      <path
        d="M6.81006 10.3702C6.81006 7.60139 7.58728 4.88827 9.0531 2.53934C9.23584 2.24651 9.62117 2.15733 9.914 2.34006C10.2068 2.52278 10.2962 2.90814 10.1136 3.20096C8.77164 5.35139 8.06006 7.83536 8.06006 10.3702C8.06006 12.9049 8.77164 15.3889 10.1136 17.5393C10.2962 17.8322 10.2068 18.2175 9.914 18.4002C9.62117 18.583 9.23584 18.4938 9.0531 18.201C7.58728 15.852 6.81006 13.1389 6.81006 10.3702Z"
        fill="currentColor"
      />
      <path
        d="M11.9398 10.3701C11.9398 7.83536 11.2286 5.35137 9.88662 3.20095C9.70388 2.90812 9.79307 2.52279 10.0859 2.34005C10.3787 2.15731 10.7641 2.2465 10.9468 2.53933C12.4126 4.88826 13.1898 7.60137 13.1898 10.3701C13.1898 13.1389 12.4126 15.852 10.9468 18.201C10.7641 18.4938 10.3787 18.583 10.0859 18.4002C9.79307 18.2175 9.70388 17.8322 9.88662 17.5393C11.2286 15.3889 11.9398 12.9049 11.9398 10.3701Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const EraseIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.94032 1.7238C9.5261 1.13802 10.4766 1.13802 11.0624 1.7238L15.3046 5.96599C15.8904 6.55178 15.8904 7.50227 15.3046 8.08806L10.0223 13.3703H14.369C14.6452 13.3703 14.869 13.5941 14.869 13.8703C14.869 14.1464 14.6451 14.3703 14.369 14.3703H7.24207L6.65809 14.3722C6.6457 14.3723 6.63334 14.3706 6.62098 14.3703H3.11903C2.84314 14.37 2.6191 14.1462 2.61903 13.8703C2.61903 13.5943 2.8431 13.3705 3.11903 13.3703H5.03016L2.22352 10.5627C1.63773 9.97688 1.63773 9.02736 2.22352 8.44158L8.94032 1.7238ZM2.93055 9.14861C2.73529 9.34387 2.73529 9.66038 2.93055 9.85564L6.29969 13.2258C6.38919 13.3153 6.50963 13.3657 6.63563 13.3703H7.23914L8.40614 13.3664C8.53809 13.366 8.66434 13.3131 8.7577 13.2199L9.40711 12.5715L4.45692 7.62126L2.93055 9.14861ZM10.3554 2.43083C10.1601 2.23557 9.84261 2.23557 9.64735 2.43083L5.16395 6.91423L10.1141 11.8644L14.5975 7.38103C14.7928 7.18577 14.7928 6.86828 14.5975 6.67302L10.3554 2.43083Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const SendIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_3922_3303)">
        <path
          d="M6.6665 9.70345L13.9998 2.37012"
          stroke="currentColor"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M14 2.37012L9.66671 14.3701C9.63746 14.4339 9.5905 14.488 9.53141 14.526C9.47233 14.5639 9.40359 14.584 9.33338 14.584C9.26317 14.584 9.19444 14.5639 9.13535 14.526C9.07626 14.488 9.0293 14.4339 9.00005 14.3701L6.66671 9.70345L2.00005 7.37012C1.93622 7.34087 1.88213 7.29391 1.84421 7.23482C1.80629 7.17573 1.78613 7.10699 1.78613 7.03678C1.78613 6.96657 1.80629 6.89784 1.84421 6.83875C1.88213 6.77966 1.93622 6.7327 2.00005 6.70345L14 2.37012Z"
          stroke="currentColor"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};

export const VoiceIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8 3.0368V13.7035"
        stroke="currentColor"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.3335 6.37012V10.3701"
        stroke="currentColor"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.3335 7.0368V9.70347"
        stroke="currentColor"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.6665 7.0368V9.70347"
        stroke="currentColor"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.6665 5.0368V11.7035"
        stroke="currentColor"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const VolumeIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.91699 3.33817C6.91699 3.06717 6.58614 2.93507 6.39949 3.13154L5.52881 4.04805C4.98844 4.61686 4.29272 5.0142 3.52825 5.19062L2.44213 5.44126C1.9884 5.54597 1.66699 5.95 1.66699 6.41565V8.32463C1.66699 8.79028 1.9884 9.19431 2.44213 9.29902L3.52825 9.54966C4.29272 9.72608 4.98844 10.1234 5.52881 10.6922L6.39949 11.6087C6.58614 11.8052 6.91699 11.6731 6.91699 11.4021V3.33817Z"
        stroke="currentColor"
        strokeWidth="0.88"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.6246 2.84738C12.7182 4.04702 13.3325 5.67386 13.3325 7.37015C13.3325 9.06644 12.7182 10.6933 11.6246 11.8929M9.56543 5.10557C10.1122 5.70538 10.4194 6.51881 10.4194 7.36695C10.4194 8.21509 10.1122 9.02851 9.56543 9.62833"
        stroke="currentColor"
        strokeWidth="0.88"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const PauseIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 14 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5.25 3.2868H4.08333C3.76117 3.2868 3.5 3.54797 3.5 3.87014V10.8701C3.5 11.1923 3.76117 11.4535 4.08333 11.4535H5.25C5.57217 11.4535 5.83333 11.1923 5.83333 10.8701V3.87014C5.83333 3.54797 5.57217 3.2868 5.25 3.2868Z"
        stroke="currentColor"
        strokeWidth="0.88"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.91699 3.2868H8.75033C8.42816 3.2868 8.16699 3.54797 8.16699 3.87014V10.8701C8.16699 11.1923 8.42816 11.4535 8.75033 11.4535H9.91699C10.2392 11.4535 10.5003 11.1923 10.5003 10.8701V3.87014C10.5003 3.54797 10.2392 3.2868 9.91699 3.2868Z"
        stroke="currentColor"
        strokeWidth="0.88"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const CopyIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.96289 1.90137C10.171 1.90137 11.1504 2.88074 11.1504 4.08887C12.3585 4.08887 13.3379 5.06824 13.3379 6.27637V10.6514C13.3379 11.8595 12.3585 12.8389 11.1504 12.8389H6.77539C5.56727 12.8389 4.58789 11.8595 4.58789 10.6514C3.41752 10.6514 2.46176 9.73225 2.40317 8.57645L2.40039 8.46387V4.08887C2.40039 2.88074 3.37977 1.90137 4.58789 1.90137H8.96289ZM6.77539 4.96387C6.05052 4.96387 5.46289 5.55149 5.46289 6.27637V10.6514C5.46289 11.3762 6.05052 11.9639 6.77539 11.9639H11.1504C11.8753 11.9639 12.4629 11.3762 12.4629 10.6514V6.27637C12.4629 5.55149 11.8753 4.96387 11.1504 4.96387H6.77539ZM4.58789 2.77637C3.86302 2.77637 3.27539 3.36399 3.27539 4.08887V8.46387L3.2771 8.53137C3.31223 9.22487 3.88566 9.77637 4.58789 9.77637V6.27637C4.58789 5.06824 5.56727 4.08887 6.77539 4.08887H10.2754C10.2754 3.36399 9.68776 2.77637 8.96289 2.77637H4.58789Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const ChatAddIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_4167_4621)">
        <path
          d="M8.86914 8.37015V10.3701M6.86914 8.37015H8.86914H6.86914ZM10.8691 8.37015H8.86914H10.8691ZM8.86914 8.37015V6.37015V8.37015Z"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.86881 15.0368C12.5507 15.0368 15.5355 12.052 15.5355 8.37015C15.5355 4.68825 12.5507 1.70348 8.86881 1.70348C5.18691 1.70348 2.20215 4.68825 2.20215 8.37015C2.20215 9.58442 2.5268 10.7229 3.09403 11.7035L2.53548 14.7035L5.53548 14.145C6.51606 14.7122 7.65455 15.0368 8.86881 15.0368Z"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_4167_4621">
          <rect
            width="1em"
            height="1em"
            fill="white"
            transform="translate(0.869141 0.370148)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const MoreIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 15 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M7.86914 10.6514C8.4732 10.6514 8.96289 11.1411 8.96289 11.7451C8.96289 12.3492 8.4732 12.8389 7.86914 12.8389C7.26508 12.8389 6.77539 12.3492 6.77539 11.7451C6.77539 11.1411 7.26508 10.6514 7.86914 10.6514ZM7.86914 6.2764C8.4732 6.2764 8.96289 6.76609 8.96289 7.37015C8.96289 7.97421 8.4732 8.4639 7.86914 8.4639C7.26508 8.4639 6.77539 7.97421 6.77539 7.37015C6.77539 6.76609 7.26508 6.2764 7.86914 6.2764ZM7.86914 1.9014C8.4732 1.9014 8.96289 2.39109 8.96289 2.99515C8.96289 3.59921 8.4732 4.0889 7.86914 4.0889C7.26508 4.0889 6.77539 3.59921 6.77539 2.99515C6.77539 2.39109 7.26508 1.9014 7.86914 1.9014Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const TeamIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_4540_2398)">
        <path
          d="M6 6.66667V2.66667C6 2.48986 6.07024 2.32029 6.19526 2.19526C6.32029 2.07024 6.48986 2 6.66667 2H13.3333C13.5101 2 13.6797 2.07024 13.8047 2.19526C13.9298 2.32029 14 2.48986 14 2.66667V14H8.66667M5.33333 6L8.66667 9.33333V14H5.33333V11.3333V6ZM5.33333 14H2V9.33333L5.33333 6V14Z"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.66669 4.66675V4.67341"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.3333 4.66675V4.67341"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.3333 7.33325V7.33992"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.3333 10V10.0067"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_4540_2398">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const DownTriangleIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.96083 5.15271L6.19902 7.9522C6.05797 8.09517 5.82774 8.09673 5.68477 7.95568L2.97854 5.14976C2.83913 5.0052 2.8433 4.775 2.98785 4.63558C3.05563 4.57022 3.14612 4.53369 3.24028 4.53369L8.70196 4.53369C8.90279 4.53369 9.0656 4.6965 9.0656 4.89733C9.0656 4.99292 9.02796 5.08466 8.96083 5.15271Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const UserGroupIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.125 2.25C9.78185 2.25 11.125 3.59315 11.125 5.25C11.125 6.41138 10.4588 7.45316 9.43231 7.95081C9.18383 8.07127 8.88474 7.96749 8.76428 7.71901C8.64382 7.47052 8.7476 7.17143 8.99608 7.05097C9.68097 6.71894 10.125 6.02456 10.125 5.25C10.125 4.14543 9.22957 3.25 8.125 3.25C7.02043 3.25 6.125 4.14543 6.125 5.25C6.125 6.10595 6.66769 6.85873 7.46215 7.13768C7.7227 7.22917 7.85975 7.51455 7.76826 7.7751C7.67678 8.03564 7.3914 8.1727 7.13085 8.08121C5.93881 7.66265 5.125 6.5338 5.125 5.25C5.125 3.59315 6.46815 2.25 8.125 2.25Z"
        fill="currentColor"
      />
      <path
        d="M4.54326 3.3464C4.80924 3.27219 5.08502 3.42765 5.15924 3.69363C5.23345 3.95961 5.07799 4.2354 4.81201 4.30961C4.11588 4.50385 3.625 5.14117 3.625 5.87504C3.625 6.49876 3.97927 7.05871 4.52817 7.33144C4.77547 7.45432 4.87633 7.7544 4.75346 8.0017C4.63058 8.249 4.3305 8.34986 4.0832 8.22698C3.19746 7.78688 2.625 6.88208 2.625 5.87504C2.625 4.68951 3.41758 3.66049 4.54326 3.3464Z"
        fill="currentColor"
      />
      <path
        d="M11.1854 3.69931C11.2712 3.43683 11.5535 3.29358 11.816 3.37936C12.8866 3.72925 13.625 4.73095 13.625 5.87493C13.625 6.82735 13.1131 7.6909 12.3015 8.15496C12.0618 8.29204 11.7564 8.20884 11.6193 7.96912C11.4822 7.7294 11.5654 7.42395 11.8051 7.28687C12.3083 6.99913 12.625 6.46493 12.625 5.87493C12.625 5.16671 12.1677 4.54636 11.5053 4.32989C11.2429 4.24412 11.0996 3.9618 11.1854 3.69931Z"
        fill="currentColor"
      />
      <path
        d="M4.14899 7.29637C4.41886 7.23786 4.68507 7.4092 4.74358 7.67908C4.80209 7.94895 4.63074 8.21515 4.36087 8.27366C2.99269 8.57029 2 9.78688 2 11.2055C2 11.4343 2.02551 11.6593 2.07554 11.878C2.13714 12.1472 1.96886 12.4153 1.69967 12.4769C1.43049 12.5385 1.16234 12.3702 1.10074 12.1011C1.03399 11.8094 1 11.5095 1 11.2055C1 9.31383 2.32319 7.69221 4.14899 7.29637Z"
        fill="currentColor"
      />
      <path
        d="M11.5139 7.63266C11.5787 7.36421 11.8488 7.19907 12.1172 7.2638C13.8995 7.69361 15.1765 9.29444 15.1765 11.1525C15.1765 11.4611 15.1415 11.7655 15.0727 12.0614C15.0101 12.3304 14.7414 12.4977 14.4724 12.4352C14.2035 12.3727 14.0361 12.1039 14.0987 11.835C14.1502 11.6132 14.1765 11.3847 14.1765 11.1525C14.1765 9.75903 13.2185 8.55804 11.8828 8.23594C11.6143 8.1712 11.4492 7.9011 11.5139 7.63266Z"
        fill="currentColor"
      />
      <path
        d="M7.69788 7.49229C7.75235 7.763 7.57705 8.02662 7.30633 8.08109C5.39613 8.46545 4 10.1518 4 12.125C4 12.2523 4.00576 12.3788 4.01718 12.5043L4.02775 12.6055L12.2224 12.6055C12.2407 12.447 12.25 12.2866 12.25 12.125C12.25 10.1684 10.877 8.49213 8.98803 8.09027C8.71794 8.03281 8.54556 7.76728 8.60302 7.49718C8.66048 7.22708 8.92602 7.0547 9.19611 7.11216C11.5446 7.61176 13.25 9.69395 13.25 12.125C13.25 12.495 13.2107 12.8603 13.1334 13.2164C13.0749 13.4863 12.8086 13.6576 12.5388 13.599L12.5095 13.5918C12.4838 13.5979 12.4573 13.6021 12.4301 13.6041L12.3928 13.6055H3.20056L3.1154 13.211C3.03891 12.8566 3 12.4931 3 12.125C3 9.67326 4.73419 7.5786 7.10907 7.10074C7.37979 7.04627 7.6434 7.22157 7.69788 7.49229Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const AddSubitemIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M11.1562 2.1875C11.8811 2.1875 12.4688 2.77513 12.4688 3.5V3.9375C12.4688 4.66237 11.8811 5.25 11.1562 5.25H3.5V8.09375C3.5 9.06025 4.2835 9.84375 5.25 9.84375H5.48828C5.59252 9.22311 6.13099 8.75 6.78125 8.75H8.09375C8.33537 8.75 8.53125 8.94588 8.53125 9.1875C8.53125 9.42912 8.33537 9.625 8.09375 9.625H6.78125C6.53963 9.625 6.34375 9.82088 6.34375 10.0625V10.5C6.34375 10.7416 6.53963 10.9375 6.78125 10.9375H11.1562C11.3979 10.9375 11.5938 10.7416 11.5938 10.5V10.2812C11.5938 10.0396 11.7896 9.84375 12.0312 9.84375C12.2729 9.84375 12.4688 10.0396 12.4688 10.2812V10.5C12.4688 11.2249 11.8811 11.8125 11.1562 11.8125H6.78125C6.13099 11.8125 5.59252 11.3394 5.48828 10.7188H5.25C3.80025 10.7188 2.625 9.5435 2.625 8.09375V5.25C2.625 5.24376 2.62669 5.23762 2.62695 5.23145C2.0053 5.12806 1.53125 4.58848 1.53125 3.9375V3.5C1.53125 2.77513 2.11888 2.1875 2.84375 2.1875H11.1562ZM10.2812 5.90625C10.5229 5.90625 10.7188 6.10213 10.7188 6.34375V7.21875H11.5938C11.8354 7.21875 12.0312 7.41463 12.0312 7.65625C12.0312 7.89787 11.8354 8.09375 11.5938 8.09375H10.7188V8.96875C10.7188 9.21037 10.5229 9.40625 10.2812 9.40625C10.0396 9.40625 9.84375 9.21037 9.84375 8.96875V8.09375H8.96875C8.72713 8.09375 8.53125 7.89787 8.53125 7.65625C8.53125 7.41463 8.72713 7.21875 8.96875 7.21875H9.84375V6.34375C9.84375 6.10213 10.0396 5.90625 10.2812 5.90625ZM2.84375 3.0625C2.60213 3.0625 2.40625 3.25838 2.40625 3.5V3.9375C2.40625 4.17912 2.60213 4.375 2.84375 4.375H11.1562C11.3979 4.375 11.5938 4.17912 11.5938 3.9375V3.5C11.5938 3.25838 11.3979 3.0625 11.1562 3.0625H2.84375Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const QuestionIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8 2C11.5899 2 14.5 4.91015 14.5 8.5C14.5 12.0899 11.5899 15 8 15C4.41015 15 1.5 12.0899 1.5 8.5C1.5 4.91015 4.41015 2 8 2ZM8 3C4.96243 3 2.5 5.46243 2.5 8.5C2.5 11.5376 4.96243 14 8 14C11.0376 14 13.5 11.5376 13.5 8.5C13.5 5.46243 11.0376 3 8 3ZM8.05176 11.0215C8.32785 11.0215 8.55168 11.2454 8.55176 11.5215C8.55158 11.7975 8.32779 12.0215 8.05176 12.0215C7.77589 12.0213 7.55193 11.7974 7.55176 11.5215C7.55183 11.2455 7.77583 11.0217 8.05176 11.0215ZM6.54688 5.44531C7.16874 4.68835 8.37443 4.52978 9.2998 5.06836C10.2614 5.62836 10.52 6.98229 9.8584 7.9043C9.74187 8.06665 9.61394 8.21754 9.46289 8.37402L9.36719 8.46973L8.9502 8.87109L8.85938 8.96094C8.85194 8.96848 8.84513 8.97588 8.83984 8.98145C8.70027 9.12863 8.62862 9.23553 8.60254 9.3291C8.55536 9.49856 8.53197 9.71052 8.53516 9.96289C8.53833 10.2387 8.3178 10.4651 8.04199 10.4688C7.76609 10.4722 7.53899 10.2514 7.53516 9.97559C7.53083 9.63367 7.56504 9.32842 7.63965 9.06055C7.71094 8.80485 7.84722 8.58733 8.05566 8.35645L8.13477 8.27148L8.28223 8.12598L8.67773 7.74512C8.8277 7.59511 8.94528 7.46146 9.0459 7.32129C9.36935 6.8705 9.23967 6.19072 8.79688 5.93262C8.27933 5.6314 7.61647 5.71826 7.31934 6.08008C7.1262 6.31534 7.01669 6.5992 7.00195 6.89551L7 6.96387C6.99983 7.23986 6.77603 7.46387 6.5 7.46387C6.22397 7.46387 6.00017 7.23986 6 6.96387C6.00005 6.41039 6.19267 5.87672 6.54688 5.44531Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const WarningIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.4858 3.56011C12.6491 3.6508 12.7837 3.78538 12.8743 3.94862L21.1748 18.8895C21.443 19.3723 21.2691 19.9811 20.7863 20.2493C20.6378 20.3318 20.4706 20.3751 20.3007 20.3751H3.69971C3.14742 20.3751 2.69971 19.9274 2.69971 19.3751C2.69971 19.2052 2.74302 19.0381 2.82555 18.8895L11.126 3.94862C11.3942 3.46584 12.0031 3.29189 12.4858 3.56011ZM11.98 15.475C11.4415 15.475 11.005 15.9115 11.005 16.45C11.005 16.9885 11.4415 17.425 11.98 17.425C12.5185 17.425 12.955 16.9885 12.955 16.45C12.955 15.9115 12.5185 15.475 11.98 15.475ZM12.925 10.875H11.05L11.425 14.875H12.55L12.925 10.875Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const StartNodeIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5.32422 1.91168C6.26614 1.99112 7.27579 2.33587 8.20703 2.65582C9.16974 2.98659 10.052 3.29169 10.8369 3.3736C11.5991 3.45304 12.185 3.31326 12.6357 2.83356C12.8248 2.63242 13.1416 2.62211 13.3428 2.8111C13.4752 2.93557 13.5218 3.11516 13.4863 3.28082C13.4948 3.31727 13.5 3.35508 13.5 3.3941V8.6441C13.5 8.90335 13.3024 9.11411 13.0498 9.13922C12.3625 9.60468 11.5487 9.69283 10.7412 9.62262C9.82937 9.54332 8.83731 9.25318 7.90723 8.98688C6.95125 8.71317 6.05398 8.46252 5.24707 8.40582C4.5397 8.35615 3.96142 8.46113 3.5 8.78766V13.8941C3.5 14.1702 3.27614 14.3941 3 14.3941C2.72386 14.3941 2.5 14.1702 2.5 13.8941V3.1441C2.50007 2.95465 2.60634 2.79137 2.76172 2.7066C3.50213 2.00249 4.41062 1.83469 5.32422 1.91168ZM12.5 6.67438C11.9466 6.88818 11.3424 6.92489 10.7412 6.87262C10.6613 6.86567 10.581 6.85531 10.5 6.84528V8.58746C10.611 8.60329 10.7205 8.61717 10.8281 8.62653C11.5209 8.68673 12.071 8.59625 12.5 8.30133V6.67438ZM7 7.70465C7.39886 7.80422 7.79689 7.91452 8.18262 8.02496C8.63822 8.15541 9.07839 8.28099 9.5 8.38531V6.66266C8.96642 6.53987 8.42691 6.38567 7.90723 6.23688C7.59858 6.14851 7.29527 6.06465 7 5.98688V7.70465ZM5.24707 5.65582C4.5397 5.60615 3.96142 5.71113 3.5 6.03766V7.65289C4.07181 7.41718 4.69501 7.36502 5.31738 7.40875C5.5415 7.4245 5.76965 7.45294 6 7.49078V5.75641C5.74143 5.70883 5.49016 5.6729 5.24707 5.65582ZM12.5 4.13824C11.9469 4.38626 11.3395 4.43107 10.7324 4.36774C10.6554 4.35969 10.5781 4.34793 10.5 4.33649V5.83746C10.611 5.85329 10.7205 5.86717 10.8281 5.87653C11.5209 5.93673 12.071 5.84625 12.5 5.55133V4.13824ZM7 4.95465C7.39886 5.05422 7.79689 5.16452 8.18262 5.27496C8.63822 5.40541 9.07839 5.53099 9.5 5.63531V4.12067C8.95556 3.97136 8.40658 3.78143 7.88184 3.60114C7.58096 3.49776 7.28628 3.40002 7 3.30914V4.95465ZM5.24023 2.90778C4.53871 2.84865 3.96527 2.97331 3.5 3.38727V4.90289C4.07181 4.66718 4.69501 4.61502 5.31738 4.65875C5.5415 4.6745 5.76965 4.70294 6 4.74078V3.03082C5.7383 2.97238 5.48503 2.92842 5.24023 2.90778Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const LLMNodeIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M8.07994 11.2966C8.15927 11.0321 8.43801 10.8819 8.7025 10.9612L11.6251 11.8379C11.8896 11.9172 12.0396 12.1959 11.9603 12.4604C11.881 12.7249 11.6022 12.875 11.3378 12.7956L8.41514 11.9192C8.1507 11.8398 8.00065 11.5611 8.07994 11.2966ZM3.6837 4.18381C3.93166 4.10941 4.19218 4.23679 4.28917 4.47092L4.3065 4.51902L5.18297 7.4404C5.26226 7.70487 5.11222 7.98361 4.84776 8.06296C4.59978 8.13737 4.33925 8.01003 4.24229 7.77585L4.2252 7.72776L3.34849 4.80662C3.26913 4.54214 3.41924 4.2632 3.6837 4.18381Z"
        fill="currentColor"
      />
      <path
        d="M11.3879 4.04938C11.5831 3.85413 11.8996 3.85416 12.0949 4.04938C12.2902 4.24464 12.2902 4.56115 12.0949 4.75641L8.13568 8.71564C7.94042 8.9109 7.62391 8.9109 7.42865 8.71564C7.23343 8.52038 7.2334 8.20386 7.42865 8.00861L11.3879 4.04938Z"
        fill="currentColor"
      />
      <path
        d="M4.0625 3.67554C4.0625 3.38214 3.82465 3.14429 3.53125 3.14429C3.23785 3.14429 3 3.38214 3 3.67554C3 3.96894 3.23785 4.20679 3.53125 4.20679C3.82465 4.20679 4.0625 3.96894 4.0625 3.67554ZM5.0625 3.67554C5.0625 4.52122 4.37694 5.20679 3.53125 5.20679C2.68556 5.20679 2 4.52122 2 3.67554C2 2.82985 2.68556 2.14429 3.53125 2.14429C4.37694 2.14429 5.0625 2.82985 5.0625 3.67554Z"
        fill="currentColor"
      />
      <path
        d="M13 3.67554C13 3.38214 12.7622 3.14429 12.4688 3.14429C12.1753 3.14429 11.9375 3.38214 11.9375 3.67554C11.9375 3.96894 12.1753 4.20679 12.4688 4.20679C12.7622 4.20679 13 3.96894 13 3.67554ZM14 3.67554C14 4.52122 13.3144 5.20679 12.4688 5.20679C11.6231 5.20679 10.9375 4.52122 10.9375 3.67554C10.9375 2.82985 11.6231 2.14429 12.4688 2.14429C13.3144 2.14429 14 2.82985 14 3.67554Z"
        fill="currentColor"
      />
      <path
        d="M13 12.613C13 12.3196 12.7622 12.0818 12.4688 12.0818C12.1753 12.0818 11.9375 12.3196 11.9375 12.613C11.9375 12.9064 12.1753 13.1443 12.4688 13.1443C12.7622 13.1443 13 12.9064 13 12.613ZM14 12.613C14 13.4587 13.3144 14.1443 12.4688 14.1443C11.6231 14.1443 10.9375 13.4587 10.9375 12.613C10.9375 11.7674 11.6231 11.0818 12.4688 11.0818C13.3144 11.0818 14 11.7674 14 12.613Z"
        fill="currentColor"
      />
      <path
        d="M8.1875 10.5505C8.1875 9.11805 7.02624 7.95679 5.59375 7.95679C4.16126 7.95679 3 9.11805 3 10.5505C3 11.983 4.16126 13.1443 5.59375 13.1443C7.02624 13.1443 8.1875 11.983 8.1875 10.5505ZM9.1875 10.5505C9.1875 12.5353 7.57852 14.1443 5.59375 14.1443C3.60898 14.1443 2 12.5353 2 10.5505C2 8.56576 3.60898 6.95679 5.59375 6.95679C7.57852 6.95679 9.1875 8.56576 9.1875 10.5505Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const ReplyNodeIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M11.1323 5.11072C11.4084 5.11076 11.6323 5.3346 11.6323 5.61072C11.6323 5.88683 11.4084 6.11067 11.1323 6.11072H4.86792C4.59178 6.11072 4.36792 5.88686 4.36792 5.61072C4.36792 5.33458 4.59178 5.11072 4.86792 5.11072H11.1323Z"
        fill="currentColor"
      />
      <path
        d="M9.56616 8.24286C9.8423 8.24286 10.0662 8.46672 10.0662 8.74286C10.0662 9.019 9.8423 9.24286 9.56616 9.24286H4.86792C4.59178 9.24286 4.36792 9.019 4.36792 8.74286C4.36792 8.46672 4.59178 8.24286 4.86792 8.24286H9.56616Z"
        fill="currentColor"
      />
      <path
        d="M13.8064 4.14429C13.8064 3.31586 13.1348 2.64429 12.3064 2.64429H3.8064C2.97797 2.64429 2.3064 3.31586 2.3064 4.14429V9.64429C2.3064 10.4727 2.97797 11.1443 3.8064 11.1443H5.17529C5.78439 11.1443 6.37262 11.3668 6.82935 11.7698L8.0564 12.8525L9.28345 11.7698C9.74017 11.3668 10.3284 11.1443 10.9375 11.1443H12.3064C13.1348 11.1443 13.8064 10.4727 13.8064 9.64429V4.14429ZM14.8064 9.64429C14.8064 11.025 13.6871 12.1443 12.3064 12.1443H10.9375C10.5721 12.1443 10.2191 12.2777 9.94507 12.5195L8.71802 13.6023C8.33999 13.9358 7.7728 13.9358 7.39478 13.6023L6.16772 12.5195C5.8937 12.2777 5.54073 12.1443 5.17529 12.1443H3.8064C2.42568 12.1443 1.3064 11.025 1.3064 9.64429V4.14429C1.3064 2.76358 2.42568 1.64429 3.8064 1.64429H12.3064C13.6871 1.64429 14.8064 2.76358 14.8064 4.14429V9.64429Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const NumberVariableIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M2.66675 11.8334V5.16669L7.33341 11.8334V5.16669"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 11.8333H13.3333"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.6667 9.16669C12.5871 9.16669 13.3333 8.27126 13.3333 7.16669C13.3333 6.06212 12.5871 5.16669 11.6667 5.16669C10.7462 5.16669 10 6.06212 10 7.16669C10 8.27126 10.7462 9.16669 11.6667 9.16669Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const TextVariableIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M4.6731 6.50002V5.16669H11.3398V6.50002"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.00643 11.8334H9.33976M8.00643 5.16669V11.8334V5.16669ZM8.00643 11.8334H6.6731H8.00643Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const RepairIcon = (props: SvgIconProps) => {
  return (
    <svg
      width="1em"
      height="1em"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M4.13904 2.31067L5.7378 3.90943C5.93298 4.1047 5.93303 4.4213 5.7378 4.61654C5.54257 4.81177 5.22596 4.81172 5.03069 4.61654L3.43193 3.01778C3.23667 2.82252 2.92009 2.82252 2.72483 3.01778L2.01772 3.72489C1.82246 3.92015 1.82246 4.23673 2.01772 4.43199L4.96906 7.38333C5.16433 7.5786 5.16433 7.89518 4.96906 8.09044C4.7738 8.2857 4.45722 8.2857 4.26196 8.09044L1.31061 5.1391C0.724827 4.55331 0.724829 3.60357 1.31061 3.01778L2.01772 2.31067C2.60351 1.72489 3.55326 1.72489 4.13904 2.31067ZM10.6857 10.9787L12.9779 13.2708C13.1731 13.4661 13.1731 13.7827 12.9779 13.9779C12.7826 14.1732 12.466 14.1732 12.2708 13.9779L9.9786 11.6858C9.78333 11.4905 9.78333 11.1739 9.9786 10.9787C10.1739 10.7834 10.4904 10.7834 10.6857 10.9787Z"
        fill="currentColor"
      />
      <path
        d="M9.44482 4.96077C9.44482 4.64885 9.5673 4.34929 9.78564 4.12654L9.78906 4.12312L9.7959 4.11604L11.8254 2.08626C11.3542 1.98231 10.8647 1.97177 10.385 2.05916C9.65509 2.19215 8.98312 2.54456 8.4585 3.06917C7.93387 3.59378 7.58148 4.26579 7.44849 4.99568C7.31552 5.72555 7.40824 6.47872 7.71411 7.15462C7.79983 7.34418 7.75917 7.56706 7.61206 7.71419L3.85229 11.474C3.67091 11.6553 3.56909 11.9015 3.56909 12.158C3.56912 12.4145 3.67094 12.6605 3.85229 12.8419C4.03368 13.0233 4.27985 13.1251 4.53638 13.1251C4.79285 13.1251 5.03885 13.0232 5.22021 12.8419L8.97974 9.08186C9.12689 8.9347 9.34994 8.89426 9.53955 8.98006C10.2155 9.28591 10.9686 9.37842 11.6985 9.24544C12.4284 9.11245 13.1006 8.76028 13.6252 8.23567C14.1498 7.71109 14.502 7.03901 14.635 6.30916C14.7224 5.82943 14.7117 5.33976 14.6077 4.86849L12.5676 6.90852C12.3449 7.12686 12.0453 7.2491 11.7334 7.2491C11.4215 7.2491 11.1219 7.12686 10.8992 6.90852L9.78906 5.79842C9.78789 5.79724 9.78681 5.79594 9.78564 5.79475C9.56732 5.57202 9.44484 5.27266 9.44482 4.96077ZM10.4448 4.96077C10.4448 5.0109 10.4647 5.059 10.4998 5.0948H10.4995L11.5994 6.19441L11.6282 6.21785C11.6592 6.2382 11.6958 6.2491 11.7334 6.2491C11.783 6.2491 11.8305 6.22976 11.8662 6.19539L14.4714 3.59041C14.5875 3.47435 14.7533 3.4228 14.9148 3.45223C15.0762 3.48168 15.2129 3.58839 15.2805 3.73787C15.6702 4.599 15.7883 5.55848 15.6189 6.48836C15.4495 7.41822 15.0006 8.27436 14.3323 8.9427C13.6639 9.61105 12.8076 10.0599 11.8777 10.2293C11.0633 10.3777 10.2262 10.3055 9.45239 10.0235L5.92725 13.5489C5.55835 13.9178 5.05806 14.1251 4.53638 14.1251C4.01465 14.1251 3.51419 13.9178 3.14526 13.5489C2.77637 13.18 2.56912 12.6797 2.56909 12.158C2.56909 11.6363 2.77634 11.1358 3.14526 10.7669L6.67041 7.24153C6.38846 6.46772 6.31622 5.63061 6.4646 4.81624C6.63404 3.8864 7.08288 3.03022 7.75122 2.36189C8.41959 1.69355 9.27591 1.2447 10.2058 1.07527C11.1357 0.905865 12.0952 1.02399 12.9563 1.41365C13.1058 1.48133 13.2125 1.61815 13.2419 1.77962C13.2713 1.94106 13.2198 2.10669 13.1038 2.22273L10.5032 4.82332L10.4993 4.82698C10.4644 4.86276 10.4448 4.91081 10.4448 4.96077Z"
        fill="currentColor"
      />
    </svg>
  );
};

export const AiWorkFlowIcons = {
  TriangleAiWorkflowIcon: forwardRef<
    SVGSVGElement,
    React.PropsWithChildren<{}>
  >((props, ref) => {
    return <TriangleAiWorkflowIcon {...props} />;
  }),
  PlusSquareAiWorkflowIcon: forwardRef<
    SVGSVGElement,
    React.PropsWithChildren<{}>
  >((props, ref) => {
    return <PlusSquareAiWorkflowIcon {...props} />;
  }),
  SlideBarAiWorkflowIcon: forwardRef<
    SVGSVGElement,
    React.PropsWithChildren<{}>
  >((props, ref) => {
    return <SlideBarAiWorkflowIcon {...props} />;
  }),
  DeleteAiWorkflowIcon: forwardRef<SVGSVGElement, React.PropsWithChildren<{}>>(
    (props, ref) => {
      return <DeleteAiWorkflowIcon {...props} />;
    },
  ),
  EditAiWorkflowIcon: forwardRef<SVGSVGElement, React.PropsWithChildren<{}>>(
    (props, ref) => {
      return <EditAiWorkflowIcon {...props} />;
    },
  ),
  RestoreAiWorkflowIcon: forwardRef<SVGSVGElement, React.PropsWithChildren<{}>>(
    (props, ref) => {
      return <RestoreAiWorkflowIcon {...props} />;
    },
  ),
  CodeAiWorkflowIcon: forwardRef<SVGSVGElement, React.PropsWithChildren<{}>>(
    (props, ref) => {
      return <CodeAiWorkflowIcon {...props} />;
    },
  ),
  WorkFlowIcon: forwardRef<SVGSVGElement, React.PropsWithChildren<{}>>(
    (props, ref) => {
      return <WorkFlowIcon {...props} />;
    },
  ),
  CopyAiWorkflowIcon: forwardRef<SVGSVGElement, React.PropsWithChildren<{}>>(
    (props, ref) => {
      return <CopyAiWorkflowIcon {...props} />;
    },
  ),
  PreviewAiWorkflowIcon: forwardRef<SVGSVGElement, React.PropsWithChildren<{}>>(
    (props, ref) => {
      return <PreviewAiWorkflowIcon {...props} />;
    },
  ),
  EyeAiWorkflowIcon: forwardRef<SVGSVGElement, React.PropsWithChildren<{}>>(
    (props, ref) => {
      return <EyeAiWorkflowIcon {...props} />;
    },
  ),
  NoEyeAiWorkflowIcon: forwardRef<SVGSVGElement, React.PropsWithChildren<{}>>(
    (props, ref) => {
      return <NoEyeAiWorkflowIcon {...props} />;
    },
  ),
};
