import { Node } from "@xyflow/react";
import { debounce } from "lodash";
import { useCallback, useRef } from "react";

import { useWorkflowStore } from "@/stores/workflowStore";
import { NodePosition } from "@/types/workflow/node";

/**
 * 节点拖拽管理Hook
 * 提供优化的节点拖拽处理逻辑，包括防抖和批量更新
 */
export function useNodeDrag() {
  const updateNode = useWorkflowStore((state) => state.updateNode);
  const updateNodePositions = useWorkflowStore(
    (state) => state.updateNodePositions,
  );

  // 存储待更新的位置信息
  const pendingUpdatesRef = useRef<Map<string, NodePosition>>(new Map());

  // 防抖的批量位置更新
  const debouncedBatchUpdate = useCallback(
    debounce(() => {
      const updates = Array.from(pendingUpdatesRef.current.entries()).map(
        ([nodeId, position]) => ({
          nodeId,
          position,
        }),
      );

      if (updates.length > 0) {
        updateNodePositions(updates);
        pendingUpdatesRef.current.clear();
      }
    }, 200),
    [updateNodePositions],
  );

  // 节点拖拽过程中的位置更新
  const handleNodeDragUpdate = useCallback(
    (nodeId: string, position: NodePosition) => {
      // 将位置更新添加到待处理队列
      pendingUpdatesRef.current.set(nodeId, position);
      // 触发防抖更新
      debouncedBatchUpdate();
    },
    [debouncedBatchUpdate],
  );

  // 节点拖拽开始事件处理
  const handleNodeDragStart = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      // 清除该节点的待更新位置（如果有的话）
      pendingUpdatesRef.current.delete(node.id);
    },
    [],
  );

  // 节点拖拽结束事件处理
  const handleNodeDragStop = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      // 取消防抖更新，立即同步最终位置
      debouncedBatchUpdate.cancel();

      // 清除该节点的待更新位置
      pendingUpdatesRef.current.delete(node.id);

      // 立即更新节点位置到store
      updateNode(node.id, { position: node.position });
    },
    [updateNode, debouncedBatchUpdate],
  );

  // 清理函数
  const cleanup = useCallback(() => {
    debouncedBatchUpdate.cancel();
    pendingUpdatesRef.current.clear();
  }, [debouncedBatchUpdate]);

  return {
    handleNodeDragUpdate,
    handleNodeDragStart,
    handleNodeDragStop,
    cleanup,
  };
}
